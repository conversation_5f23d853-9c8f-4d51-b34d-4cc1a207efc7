{% extends "base.html" %}

{% block title %}Subscription Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Subscription Plans</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Price</th>
                                <th>Duration (days)</th>
                                <th>Features</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for subscription in subscriptions %}
                            <tr>
                                <td>{{ subscription.id }}</td>
                                <td>{{ subscription.name|capitalize }}</td>
                                <td>${{ subscription.price }}</td>
                                <td>{{ subscription.duration_days }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#features{{ subscription.id }}" aria-expanded="false" aria-controls="features{{ subscription.id }}">
                                        View Features
                                    </button>
                                    <div class="collapse mt-2" id="features{{ subscription.id }}">
                                        <div class="card card-body">
                                            <pre>{{ subscription.features }}</pre>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ subscription.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('admin_edit_subscription', subscription_id=subscription.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i> Edit
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
