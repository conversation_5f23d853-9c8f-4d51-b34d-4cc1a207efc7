import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, session
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
import datetime
import json
import os
import configparser
import pymysql
import threading
from sqlalchemy import create_engine
from werkzeug.urls import url_parse
from werkzeug.security import generate_password_hash, check_password_hash

# Import our models and forms
from models import User, Subscription, Watchlist, WatchlistItem, create_tables, get_db_connection
from forms import (LoginForm, RegistrationForm, SubscriptionForm, WatchlistForm, AddStockForm,
                  UserCreateForm, UserEditForm, SubscriptionEditForm)
import fii
import advance_fii

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'  # Change this to a random secret key
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(days=7)
app.config['REGISTERED_ENDPOINTS'] = ['admin_dashboard', 'admin_users', 'admin_subscriptions']

# Initialize Flask-Login
login_manager = LoginManager(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.get_by_id(user_id)

# Ensure all required tables exist
create_tables()

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Function to get data from the database
def get_data_from_db(query, params=()):
    mysql_config = get_mysql_config()

    try:
        # Create connection
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Convert SQLite-style placeholders (?) to MySQL-style placeholders (%s)
        query = query.replace('?', '%s')

        # Execute query
        df = pd.read_sql_query(query, conn, params=params)

        # Close connection
        conn.close()

        return df
    except Exception as e:
        print(f"Database error: {e}")
        return pd.DataFrame()

# Routes for authentication
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.get_by_username(form.username.data)
        if user is None or not user.check_password(form.password.data):
            flash('Invalid username or password', 'danger')
            return redirect(url_for('login'))

        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('dashboard')
        return redirect(next_page)

    return render_template('auth/login.html', title='Sign In', form=form)

@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='basic',
            subscription_type='free',
            subscription_expiry=datetime.datetime.now() + datetime.timedelta(days=30)  # Free 30-day trial
        )
        user.set_password(form.password.data)
        if user.save():
            flash('Congratulations, you are now a registered user!', 'success')
            return redirect(url_for('login'))
        else:
            flash('Registration failed. Please try again.', 'danger')

    return render_template('auth/register.html', title='Register', form=form)

# Routes for main pages
@app.route('/')
def index():
    # Show the simplified homepage
    return render_template('index_simplified.html', title='Home')

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's watchlists
    watchlists = Watchlist.get_by_user_id(current_user.id)

    # Get subscription info
    subscription = Subscription.get_by_name(current_user.subscription_type)

    # Create a new watchlist form
    watchlist_form = WatchlistForm()

    # Create a form for adding stocks to watchlist
    add_stock_form = AddStockForm()

    return render_template(
        'dashboard/index.html',
        title='Dashboard',
        watchlists=watchlists,
        subscription=subscription,
        watchlist_form=watchlist_form,
        add_stock_form=add_stock_form
    )

@app.route('/option-data')
@login_required
def option_data():
    """
    Option Chain Analysis Dashboard
    Shows live data, historical data, OI data, OI charts, and orders log
    """
    return render_template('dashboard/option_data.html', title='Option Data Dashboard')

@app.route('/profile')
@login_required
def user_profile():
    """
    User Profile Page
    Shows user information and subscription details
    """
    # Get subscription info
    subscription = Subscription.get_by_name(current_user.subscription_type)

    # Parse features JSON if it exists
    if subscription and subscription.features:
        try:
            if isinstance(subscription.features, str):
                subscription.features = json.loads(subscription.features)
        except json.JSONDecodeError:
            # If JSON parsing fails, keep it as a string
            pass

    return render_template(
        'dashboard/user_profile.html',
        title='User Profile',
        subscription=subscription,
        now=datetime.datetime.now
    )

@app.route('/fii-dii')
@login_required
def fii_dii():
    """
    FII/DII Data Dashboard
    Shows FII/DII data and analysis
    """
    return render_template('dashboard/fii_dii.html', title='FII/DII Data')

@app.route('/advance-fii')
@login_required
def advance_fii_page():
    """
    Advanced FII Data Dashboard
    Shows detailed FII/DII/PRO/CLIENT data with analytical tools
    """
    return render_template('dashboard/advance_fii.html', title='Advanced FII Analysis')

@app.route('/subscriptions', methods=['GET', 'POST'])
@login_required
def subscriptions():
    form = SubscriptionForm()
    subscriptions = Subscription.get_all()

    # Parse features JSON for each subscription
    for subscription in subscriptions:
        if subscription.features:
            try:
                if isinstance(subscription.features, str):
                    subscription.features = json.loads(subscription.features)
            except json.JSONDecodeError:
                # If JSON parsing fails, keep it as a string
                pass

    if form.validate_on_submit():
        subscription = Subscription.get_by_name(form.plan.data)
        if subscription:
            # In a real application, you would process payment here
            # For now, we'll just update the user's subscription
            user = current_user
            user.subscription_type = subscription.name
            user.subscription_expiry = datetime.datetime.now() + datetime.timedelta(days=subscription.duration_days)
            if user.save():
                flash(f'Successfully subscribed to {subscription.name} plan!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('Failed to update subscription. Please try again.', 'danger')
        else:
            flash('Invalid subscription plan selected.', 'danger')

    return render_template('dashboard/subscriptions.html', title='Subscriptions', form=form, subscriptions=subscriptions)

# Routes for watchlist management
@app.route('/watchlist/create', methods=['POST'])
@login_required
def create_watchlist():
    form = WatchlistForm()
    if form.validate_on_submit():
        # Check if user has reached their watchlist limit
        watchlists = Watchlist.get_by_user_id(current_user.id)
        subscription = Subscription.get_by_name(current_user.subscription_type)

        if subscription and len(watchlists) >= json.loads(subscription.features).get('max_watchlists', 1):
            flash(f'You have reached your limit of {json.loads(subscription.features).get("max_watchlists", 1)} watchlists. Upgrade your subscription to create more.', 'warning')
            return redirect(url_for('dashboard'))

        watchlist = Watchlist(
            user_id=current_user.id,
            name=form.name.data
        )
        if watchlist.save():
            flash(f'Watchlist "{form.name.data}" created successfully!', 'success')
        else:
            flash('Failed to create watchlist. Please try again.', 'danger')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{getattr(form, field).label.text}: {error}', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/delete', methods=['POST'])
@login_required
def delete_watchlist(watchlist_id):
    watchlist = Watchlist.get_by_id(watchlist_id)
    if watchlist and watchlist.user_id == current_user.id:
        if watchlist.delete():
            flash(f'Watchlist "{watchlist.name}" deleted successfully!', 'success')
        else:
            flash('Failed to delete watchlist. Please try again.', 'danger')
    else:
        flash('Watchlist not found or you do not have permission to delete it.', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/add_stock', methods=['POST'])
@login_required
def add_stock_to_watchlist(watchlist_id):
    form = AddStockForm()
    if form.validate_on_submit():
        watchlist = Watchlist.get_by_id(watchlist_id)
        if watchlist and watchlist.user_id == current_user.id:
            # Check if user has reached their stocks per watchlist limit
            subscription = Subscription.get_by_name(current_user.subscription_type)
            if subscription and len(watchlist.stocks) >= json.loads(subscription.features).get('max_stocks_per_watchlist', 5):
                message = f'You have reached your limit of {json.loads(subscription.features).get("max_stocks_per_watchlist", 5)} stocks per watchlist. Upgrade your subscription to add more.'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'error': message}), 400
                flash(message, 'warning')
                return redirect(url_for('dashboard'))

            # Check if stock already exists in watchlist
            for stock in watchlist.stocks:
                if stock.instrument_key == form.instrument_key.data:
                    message = f'Stock {form.instrument_key.data} is already in your watchlist.'
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify({'error': message}), 400
                    flash(message, 'warning')
                    return redirect(url_for('dashboard'))

            # Check if the stock exists in the livestocks table
            conn = get_db_connection()
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT instrument_key FROM livestocks WHERE instrument_key = %s", (form.instrument_key.data,))
                if not cursor.fetchone():
                    message = f'Stock {form.instrument_key.data} not found in our database.'
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify({'error': message}), 404
                    flash(message, 'danger')
                    return redirect(url_for('dashboard'))
            finally:
                cursor.close()
                conn.close()

            # Add stock to watchlist
            watchlist_item = WatchlistItem(
                watchlist_id=watchlist.id,
                instrument_key=form.instrument_key.data
            )
            if watchlist_item.save():
                message = f'Stock {form.instrument_key.data} added to watchlist successfully!'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': True, 'message': message})
                flash(message, 'success')
            else:
                message = 'Failed to add stock to watchlist. Please try again.'
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'error': message}), 500
                flash(message, 'danger')
        else:
            message = 'Watchlist not found or you do not have permission to modify it.'
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': message}), 403
            flash(message, 'danger')
    else:
        errors = []
        for field, field_errors in form.errors.items():
            for error in field_errors:
                errors.append(f'{getattr(form, field).label.text}: {error}')
                if not request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    flash(f'{getattr(form, field).label.text}: {error}', 'danger')

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': ', '.join(errors)}), 400

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/remove_stock/<int:stock_id>', methods=['POST'])
@login_required
def remove_stock_from_watchlist(watchlist_id, stock_id):
    # Print debug information
    print(f"Removing stock {stock_id} from watchlist {watchlist_id}")
    print(f"Request headers: {request.headers}")
    print(f"Request data: {request.form}")

    watchlist = Watchlist.get_by_id(watchlist_id)
    if not watchlist:
        print(f"Watchlist {watchlist_id} not found")
        message = 'Watchlist not found.'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': message}), 404
        flash(message, 'danger')
        return redirect(url_for('dashboard'))

    if watchlist.user_id != current_user.id:
        print(f"User {current_user.id} does not have permission to modify watchlist {watchlist_id}")
        message = 'You do not have permission to modify this watchlist.'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': message}), 403
        flash(message, 'danger')
        return redirect(url_for('dashboard'))

    # Find the stock in the watchlist
    print(f"Looking for stock with ID {stock_id} in watchlist {watchlist_id}")
    print(f"Watchlist stocks: {[(stock.id, stock.instrument_key) for stock in watchlist.stocks]}")

    stock_to_delete = None
    for stock in watchlist.stocks:
        print(f"Checking stock: ID={stock.id}, instrument_key={stock.instrument_key}")
        if stock.id == stock_id:
            stock_to_delete = stock
            print(f"Found matching stock: {stock.id}, {stock.instrument_key}")
            break

    if not stock_to_delete:
        print(f"Stock {stock_id} not found in watchlist {watchlist_id}")
        message = 'Stock not found in watchlist.'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': message}), 404
        flash(message, 'danger')
        return redirect(url_for('dashboard'))

    # Delete the stock
    if stock_to_delete.delete():
        print(f"Stock {stock_id} successfully deleted from watchlist {watchlist_id}")
        message = 'Stock removed from watchlist successfully!'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': True, 'message': message})
        flash(message, 'success')
    else:
        print(f"Failed to delete stock {stock_id} from watchlist {watchlist_id}")
        message = 'Failed to remove stock from watchlist. Please try again.'
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': message}), 500
        flash(message, 'danger')

    return redirect(url_for('dashboard'))

# Admin routes
@app.route('/admin')
@login_required
def admin_dashboard():
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get counts for dashboard
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Count users
        cursor.execute("SELECT COUNT(*) as count FROM users")
        user_count = cursor.fetchone()['count']

        # Count active subscriptions
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE subscription_expiry > NOW()")
        active_subscriptions = cursor.fetchone()['count']

        # Count watchlists
        cursor.execute("SELECT COUNT(*) as count FROM watchlists")
        watchlist_count = cursor.fetchone()['count']

        # Get subscription distribution
        cursor.execute("""
        SELECT subscription_type, COUNT(*) as count
        FROM users
        GROUP BY subscription_type
        """)
        subscription_distribution = cursor.fetchall()

        return render_template(
            'admin/index.html',
            title='Admin Dashboard',
            user_count=user_count,
            active_subscriptions=active_subscriptions,
            watchlist_count=watchlist_count,
            subscription_distribution=subscription_distribution
        )
    finally:
        cursor.close()
        conn.close()

@app.route('/admin/users')
@login_required
def admin_users():
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get all users
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("""
        SELECT * FROM users
        ORDER BY created_at DESC
        """)
        users = cursor.fetchall()

        return render_template(
            'admin/users.html',
            title='User Management',
            users=users,
            now=datetime.datetime.now
        )
    finally:
        cursor.close()
        conn.close()

@app.route('/admin/users/create', methods=['GET', 'POST'])
@login_required
def admin_create_user():
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    form = UserCreateForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            role=form.role.data,
            subscription_type=form.subscription_type.data,
            subscription_expiry=form.subscription_expiry.data,
            is_active=form.is_active.data
        )
        user.set_password(form.password.data)
        if user.save():
            flash(f'User {form.username.data} created successfully!', 'success')
            return redirect(url_for('admin_users'))
        else:
            flash('Failed to create user. Please try again.', 'danger')

    return render_template(
        'admin/create_user.html',
        title='Create User',
        form=form
    )

@app.route('/admin/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_edit_user(user_id):
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get user
    user = User.get_by_id(user_id)
    if not user:
        flash('User not found.', 'danger')
        return redirect(url_for('admin_users'))

    form = UserEditForm(original_username=user.username, original_email=user.email)

    if request.method == 'GET':
        # Populate form with user data
        form.username.data = user.username
        form.email.data = user.email
        form.role.data = user.role
        form.subscription_type.data = user.subscription_type
        form.subscription_expiry.data = user.subscription_expiry
        form.is_active.data = user.is_active

    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.role = form.role.data
        user.subscription_type = form.subscription_type.data
        user.subscription_expiry = form.subscription_expiry.data
        user.is_active = form.is_active.data

        # Update password if provided
        if form.password.data:
            user.set_password(form.password.data)

        if user.save():
            flash(f'User {user.username} updated successfully!', 'success')
            return redirect(url_for('admin_users'))
        else:
            flash('Failed to update user. Please try again.', 'danger')

    return render_template(
        'admin/edit_user.html',
        title='Edit User',
        form=form,
        user=user
    )

@app.route('/admin/users/<int:user_id>/delete', methods=['POST'])
@login_required
def admin_delete_user(user_id):
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get user
    user = User.get_by_id(user_id)
    if not user:
        flash('User not found.', 'danger')
        return redirect(url_for('admin_users'))

    # Don't allow deleting yourself
    if user.id == current_user.id:
        flash('You cannot delete your own account.', 'danger')
        return redirect(url_for('admin_users'))

    # Delete user
    if user.delete():
        flash(f'User {user.username} deleted successfully!', 'success')
    else:
        flash('Failed to delete user. Please try again.', 'danger')

    return redirect(url_for('admin_users'))

@app.route('/admin/subscriptions')
@login_required
def admin_subscriptions():
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get all subscriptions
    subscriptions = Subscription.get_all()

    return render_template(
        'admin/subscriptions.html',
        title='Subscription Management',
        subscriptions=subscriptions
    )

@app.route('/admin/subscriptions/<int:subscription_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_edit_subscription(subscription_id):
    # Check if user is admin
    if current_user.role != 'admin':
        flash('Access denied. Admin privileges required.', 'danger')
        return redirect(url_for('dashboard'))

    # Get subscription
    subscription = Subscription.get_by_id(subscription_id)
    if not subscription:
        flash('Subscription not found.', 'danger')
        return redirect(url_for('admin_subscriptions'))

    form = SubscriptionEditForm()

    if request.method == 'GET':
        # Populate form with subscription data
        form.name.data = subscription.name
        form.price.data = subscription.price
        form.duration_days.data = subscription.duration_days
        form.features.data = subscription.features

    if form.validate_on_submit():
        subscription.name = form.name.data
        subscription.price = form.price.data
        subscription.duration_days = form.duration_days.data
        subscription.features = form.features.data

        if subscription.save():
            flash(f'Subscription {subscription.name} updated successfully!', 'success')
            return redirect(url_for('admin_subscriptions'))
        else:
            flash('Failed to update subscription. Please try again.', 'danger')

    return render_template(
        'admin/edit_subscription.html',
        title='Edit Subscription',
        form=form,
        subscription=subscription
    )

# API routes for live stock data
@app.route('/api/stocks/search', methods=['GET'])
@login_required
def search_stocks():
    query = request.args.get('q', '')
    if not query or len(query) < 2:
        return jsonify([])

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("""
        SELECT instrument_key, instrument_token, last_price
        FROM livestocks
        WHERE instrument_key LIKE %s
        LIMIT 20
        """, (f'%{query}%',))

        stocks = []
        for stock in cursor.fetchall():
            stocks.append({
                'instrument_key': stock['instrument_key'],
                'instrument_token': stock['instrument_token'],
                'last_price': stock['last_price']
            })

        return jsonify(stocks)
    finally:
        cursor.close()
        conn.close()

@app.route('/api/watchlist/<int:watchlist_id>/stocks', methods=['GET'])
@login_required
def get_watchlist_stocks(watchlist_id):
    watchlist = Watchlist.get_by_id(watchlist_id)
    if not watchlist or watchlist.user_id != current_user.id:
        return jsonify({'error': 'Watchlist not found or access denied'}), 404

    # Get the refresh rate based on user's subscription
    subscription = Subscription.get_by_name(current_user.subscription_type)
    refresh_rate = 60  # Default refresh rate in seconds
    if subscription:
        refresh_rate = json.loads(subscription.features).get('refresh_rate', 60)

    # Get live data for all stocks in the watchlist
    # Create a map of instrument_key to watchlist_item_id
    watchlist_item_map = {stock.instrument_key: stock.id for stock in watchlist.stocks}
    stock_keys = list(watchlist_item_map.keys())

    if not stock_keys:
        return jsonify({'stocks': [], 'refresh_rate': refresh_rate})

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Use placeholders for each stock key
        placeholders = ', '.join(['%s'] * len(stock_keys))
        query = f"""
        SELECT * FROM livestocks
        WHERE instrument_key IN ({placeholders})
        """

        cursor.execute(query, stock_keys)

        stocks = []
        for stock in cursor.fetchall():
            # Get the watchlist_item_id for this instrument_key
            watchlist_item_id = watchlist_item_map.get(stock['instrument_key'])

            stocks.append({
                'id': watchlist_item_id,  # Use the watchlist_item_id instead of the livestocks id
                'livestocks_id': stock['id'],  # Keep the original id as well for reference
                'instrument_key': stock['instrument_key'],
                'instrument_token': stock['instrument_token'],
                'last_price': stock['last_price'],
                'prev_open': stock['prev_open'],
                'prev_high': stock['prev_high'],
                'prev_low': stock['prev_low'],
                'prev_close': stock['prev_close'],
                'prev_volume': stock['prev_volume'],
                'prev_ts': stock['prev_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['prev_ts'] else None,
                'live_open': stock['live_open'],
                'live_high': stock['live_high'],
                'live_low': stock['live_low'],
                'live_close': stock['live_close'],
                'live_volume': stock['live_volume'],
                'live_ts': stock['live_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['live_ts'] else None,
                'timestamp': stock['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if stock['timestamp'] else None
            })

        return jsonify({'stocks': stocks, 'refresh_rate': refresh_rate})
    finally:
        cursor.close()
        conn.close()

# Keep the original API routes from the existing app.py
# Function to extract symbol from timestamp
def extract_symbol(timestamp):
    parts = timestamp.split()
    if len(parts) > 3 and parts[3] == "symbol":  # Format: "YYYY-MM-DD HH:MM:SS Symbol symbol"
        return parts[2]
    return "Unknown"

# Function to extract symbols from database
def extract_symbols_from_db():
    # Try to get symbols directly from the live tables first
    query = "SELECT DISTINCT symbol FROM option_data_need_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Try to get symbols from option_data_live table
    query = "SELECT DISTINCT symbol FROM option_data_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from historical tables
    query = "SELECT DISTINCT symbol FROM option_data_need WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from option_data table
    query = "SELECT DISTINCT symbol FROM option_data WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Common symbols to look for in timestamps
    common_symbols = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'SENSEX', 'MIDCPNIFTY']
    symbols = set()

    # Get all unique timestamps
    query = "SELECT DISTINCT timestamp FROM option_data_need"
    df = get_data_from_db(query)

    # Try to extract symbols from timestamps
    if not df.empty and 'timestamp' in df.columns:
        for timestamp in df['timestamp']:
            if timestamp and isinstance(timestamp, str):
                parts = timestamp.split()
                if len(parts) > 2:
                    potential_symbol = parts[2]
                    if potential_symbol in common_symbols:
                        symbols.add(potential_symbol)

    # If no symbols found, try to infer from strike prices
    if not symbols:
        query = "SELECT DISTINCT Strike FROM option_data"
        strikes_df = get_data_from_db(query)

        # Convert strikes to float where possible
        strikes = []
        if not strikes_df.empty and 'Strike' in strikes_df.columns:
            for s in strikes_df['Strike']:
                try:
                    strikes.append(float(s))
                except (ValueError, TypeError):
                    pass

            # Nifty is typically around 15000-25000, BankNifty around 30000-50000
            if any(15000 <= s <= 25000 for s in strikes):
                symbols.add('NIFTY')
            if any(30000 <= s <= 50000 for s in strikes):
                symbols.add('BANKNIFTY')

    # If still no symbols found, use default list
    if not symbols:
        symbols = {'NIFTY', 'BANKNIFTY', 'FINNIFTY'}

    return list(symbols)

# API to get available symbols
@app.route('/api/symbols')
@login_required
def get_symbols():
    symbols = extract_symbols_from_db()
    return jsonify(symbols)

# API to get latest data for a specific symbol
@app.route('/api/latest/<symbol>')
@login_required
def get_latest_data(symbol):
    # Get latest data from option_data_need_live for the specified symbol
    query = "SELECT * FROM option_data_need_live WHERE symbol = ? LIMIT 1"
    df_need = get_data_from_db(query, (symbol,))

    # If no data found in live table, try the historical table
    if df_need.empty:
        query = "SELECT * FROM option_data_need WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1"
        df_need = get_data_from_db(query, (symbol,))

        # If still no data found for this symbol, try getting the latest data regardless of symbol
        if df_need.empty:
            query = "SELECT * FROM option_data_need_live LIMIT 1"
            df_need = get_data_from_db(query)

            if df_need.empty:
                query = "SELECT * FROM option_data_need ORDER BY timestamp DESC LIMIT 1"
                df_need = get_data_from_db(query)

                if df_need.empty:
                    return jsonify({"error": "No data found"})

    # Convert to dictionary
    data_need = df_need.to_dict('records')[0]

    # Ensure symbol is set correctly
    data_need['symbol'] = symbol

    # Get option data from option_data_live for this symbol
    query = "SELECT * FROM option_data_live WHERE symbol = ? ORDER BY Strike"
    df_option = get_data_from_db(query, (symbol,))

    # If no data in live table, try getting from historical table
    if df_option.empty:
        # Get the latest timestamp for this symbol from historical table
        query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
        latest_ts_df = get_data_from_db(query, (symbol,))

        if latest_ts_df.empty or pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
            # Try getting data for any symbol from live table
            query = "SELECT * FROM option_data_live ORDER BY Strike LIMIT 1"
            df_option = get_data_from_db(query)

            if df_option.empty:
                return jsonify({
                    "error": "No option data found for this symbol"
                })
        else:
            latest_timestamp = latest_ts_df['latest_timestamp'].iloc[0]
            # Get all strikes for the latest timestamp from historical table
            query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp = ? ORDER BY Strike"
            df_option = get_data_from_db(query, (symbol, latest_timestamp))

            # If still no data, try any symbol
            if df_option.empty:
                query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data"
                latest_ts_df = get_data_from_db(query)

                if latest_ts_df.empty or pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                    return jsonify({
                        "error": "No option data found"
                    })

                latest_timestamp = latest_ts_df['latest_timestamp'].iloc[0]
                query = "SELECT * FROM option_data WHERE timestamp = ? ORDER BY Strike"
                df_option = get_data_from_db(query, (latest_timestamp,))

    # Get the latest timestamp from the data
    if 'timestamp' in df_option.columns and not df_option.empty:
        latest_timestamp = df_option['timestamp'].iloc[0]
    else:
        latest_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Extract strike prices from the Strike column
    option_data = []
    for _, row in df_option.iterrows():
        strike = row['Strike']
        option_data.append({
            'strike': strike,
            'data': row.to_dict()
        })

    # Sort by strike price
    option_data.sort(key=lambda x: float(x['strike']) if x['strike'] != "Unknown" else 0)

    return jsonify({
        "summary": data_need,
        "option_chain": option_data,
        "timestamp": latest_timestamp
    })

# API to get historical data
@app.route('/api/historical')
@login_required
def get_historical_data():
    symbol = request.args.get('symbol', '')
    date = request.args.get('date', datetime.datetime.now().strftime('%Y-%m-%d'))
    time = request.args.get('time', '')

    # Get historical data from option_data_need for the specified symbol and date
    query = "SELECT * FROM option_data_need WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC"
    df_need = get_data_from_db(query, (symbol, f'{date}%'))

    # If no data found for this symbol and date, try getting data for just the date
    if df_need.empty:
        query = "SELECT * FROM option_data_need WHERE timestamp LIKE ? ORDER BY timestamp DESC"
        df_need = get_data_from_db(query, (f'{date}%',))

        if df_need.empty:
            return jsonify({"error": "No historical data found for this date"})

    # Ensure symbol is set correctly in each record
    for i in range(len(df_need)):
        df_need.at[i, 'symbol'] = symbol

    # Convert to dictionary
    data_need = df_need.to_dict('records')

    # Get all timestamps for the specified date to populate the time dropdown
    query = "SELECT DISTINCT timestamp FROM option_data WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC"
    timestamps_df = get_data_from_db(query, (symbol, f'{date}%'))

    timestamps = []
    if not timestamps_df.empty:
        timestamps = timestamps_df['timestamp'].tolist()

    # If a specific time is provided, get data for that time
    if time:
        full_timestamp = f"{date} {time}"
        query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp = ? ORDER BY Strike"
        df_option = get_data_from_db(query, (symbol, full_timestamp))
    else:
        # Otherwise, get all data for the date
        query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC, Strike"
        df_option = get_data_from_db(query, (symbol, f'{date}%'))

    # If no option data found for this symbol and date, try getting data for just the date
    if df_option.empty:
        if time:
            full_timestamp = f"{date} {time}"
            query = "SELECT * FROM option_data WHERE timestamp = ? ORDER BY Strike"
            df_option = get_data_from_db(query, (full_timestamp,))
        else:
            query = "SELECT * FROM option_data WHERE timestamp LIKE ? ORDER BY timestamp DESC, Strike"
            df_option = get_data_from_db(query, (f'{date}%',))

    # Group by timestamp
    option_data = {}
    for timestamp in df_option['timestamp'].unique():
        time_parts = timestamp.split()
        if len(time_parts) >= 2:
            time_key = time_parts[0] + " " + time_parts[1]  # YYYY-MM-DD HH:MM:SS
        else:
            time_key = timestamp  # Use the full timestamp if it can't be split

        if time_key not in option_data:
            option_data[time_key] = []

        # Get all rows with this timestamp
        rows = df_option[df_option['timestamp'] == timestamp]
        for _, row in rows.iterrows():
            strike = row['Strike']
            option_data[time_key].append({
                'strike': strike,
                'data': row.to_dict()
            })

    # Sort each time's data by strike price
    for time_key in option_data:
        option_data[time_key].sort(key=lambda x: float(x['strike']) if x['strike'] != "Unknown" else 0)

    return jsonify({
        "summary": data_need,
        "option_chain": option_data,
        "timestamps": timestamps
    })

# API to get available dates
@app.route('/api/dates')
@login_required
def get_dates():
    symbol = request.args.get('symbol', '')

    # If symbol is provided, get dates for that symbol
    if symbol:
        query = "SELECT DISTINCT timestamp FROM option_data WHERE symbol = ? ORDER BY timestamp DESC"
        df = get_data_from_db(query, (symbol,))
    else:
        # Get all dates regardless of symbol
        query = "SELECT DISTINCT timestamp FROM option_data ORDER BY timestamp DESC"
        df = get_data_from_db(query)

    dates = []
    if not df.empty and 'timestamp' in df.columns:
        for timestamp in df['timestamp']:
            if timestamp and isinstance(timestamp, str):
                try:
                    date = timestamp.split()[0]  # YYYY-MM-DD
                    if date not in dates:
                        dates.append(date)
                except Exception:
                    pass

    # If no dates found, use today's date
    if not dates:
        dates = [datetime.datetime.now().strftime('%Y-%m-%d')]

    return jsonify(dates)

# API to get OI data for all symbols
@app.route('/api/oi-data')
@login_required
def get_oi_data():
    # Get all symbols
    symbols = extract_symbols_from_db()

    # Get latest data for each symbol
    result = []

    # Process each symbol
    for symbol in symbols:
        # Get the latest data for this symbol from live table
        query = "SELECT * FROM option_data_need_live WHERE symbol = ? LIMIT 1"
        df_symbol = get_data_from_db(query, (symbol,))

        if not df_symbol.empty:
            # Use the data for this symbol from live table
            symbol_data = df_symbol.iloc[0].to_dict()

            # Get the latest timestamp for option data from live table
            query = "SELECT timestamp FROM option_data_live WHERE symbol = ? LIMIT 1"
            latest_ts_df = get_data_from_db(query, (symbol,))

            if not latest_ts_df.empty and not pd.isna(latest_ts_df['timestamp'].iloc[0]):
                symbol_data['latest_option_timestamp'] = latest_ts_df['timestamp'].iloc[0]

            result.append(symbol_data)
        else:
            # If no data in live table, try historical table
            query = "SELECT * FROM option_data_need WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1"
            df_symbol = get_data_from_db(query, (symbol,))

            if not df_symbol.empty:
                # Use the data for this symbol from historical table
                symbol_data = df_symbol.iloc[0].to_dict()

                # Get the latest timestamp for option data from historical table
                query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
                latest_ts_df = get_data_from_db(query, (symbol,))

                if not latest_ts_df.empty and not pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                    symbol_data['latest_option_timestamp'] = latest_ts_df['latest_timestamp'].iloc[0]

                result.append(symbol_data)
            else:
                # If no data found for this symbol in either table, try getting the latest data from live table
                query = "SELECT * FROM option_data_need_live LIMIT 1"
                df_latest = get_data_from_db(query)

                if not df_latest.empty:
                    # Use the latest data but set the symbol
                    symbol_data = df_latest.iloc[0].to_dict()
                    symbol_data['symbol'] = symbol

                    # Get the latest timestamp for option data
                    query = "SELECT timestamp FROM option_data_live WHERE symbol = ? LIMIT 1"
                    latest_ts_df = get_data_from_db(query, (symbol,))

                    if not latest_ts_df.empty and not pd.isna(latest_ts_df['timestamp'].iloc[0]):
                        symbol_data['latest_option_timestamp'] = latest_ts_df['timestamp'].iloc[0]

                    result.append(symbol_data)
                else:
                    # If no data in live table, try historical table
                    query = "SELECT * FROM option_data_need ORDER BY timestamp DESC LIMIT 1"
                    df_latest = get_data_from_db(query)

                    if not df_latest.empty:
                        # Use the latest data but set the symbol
                        symbol_data = df_latest.iloc[0].to_dict()
                        symbol_data['symbol'] = symbol

                        # Get the latest timestamp for option data
                        query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
                        latest_ts_df = get_data_from_db(query, (symbol,))

                        if not latest_ts_df.empty and not pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                            symbol_data['latest_option_timestamp'] = latest_ts_df['latest_timestamp'].iloc[0]

                        result.append(symbol_data)

    # If still no data, create dummy data for each symbol
    if not result:
        for symbol in symbols:
            result.append({
                'symbol': symbol,
                'FUT_OI': 0,
                'FUT_Change_in_OI': 0,
                'Max_Call_OI': 0,
                'Max_Put_OI': 0,
                'Max_Call_OI_Strike': 0,
                'Max_Put_OI_Strike': 0,
                'Max_Call_Change_in_OI': 0,
                'Max_Put_Change_in_OI': 0,
                'Max_Call_Change_in_OI_Strike': 0,
                'Max_Put_Change_in_OI_Strike': 0,
                'Spot_LTP': 0,
                'timestamp': 'No data available',
                'latest_option_timestamp': None
            })

    return jsonify(result)

# Ensure ORDER_LOG table exists
def ensure_order_log_table_exists():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if ORDER_LOG table exists
        cursor.execute("SHOW TABLES LIKE 'ORDER_LOG'")
        table_exists = cursor.fetchone()

        if not table_exists:
            print("Creating ORDER_LOG table...")
            cursor.execute("""
            CREATE TABLE ORDER_LOG (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME,
                symbol VARCHAR(50),
                order_type VARCHAR(10),  -- BUY or SELL
                option_type VARCHAR(5),   -- CE or PE
                strike FLOAT,
                spot_price FLOAT,
                option_price FLOAT,
                reason VARCHAR(255),      -- Reason for the trade
                support FLOAT,            -- Support level at time of trade
                resistance FLOAT,         -- Resistance level at time of trade
                pnl FLOAT,               -- P&L for the trade (NULL for BUY orders)
                status VARCHAR(20)        -- EXECUTED, FAILED, etc.
            )
            """)
            conn.commit()
            print("ORDER_LOG table created successfully")

            # Insert some sample data for testing
            cursor.execute("""
            INSERT INTO ORDER_LOG (timestamp, symbol, order_type, option_type, strike, spot_price, option_price, reason, support, resistance, status)
            VALUES (NOW(), 'NIFTY', 'BUY', 'CE', 22000, 22050.5, 150.25, 'Support crossed from below to above', 21900, 22100, 'EXECUTED')
            """)
            conn.commit()
            print("Sample data inserted into ORDER_LOG table")
        else:
            print("ORDER_LOG table already exists")

        cursor.close()
        conn.close()
    except Exception as e:
        print(f"Error ensuring ORDER_LOG table exists: {e}")

# Call the function to ensure the table exists
ensure_order_log_table_exists()

# API to get order log data
@app.route('/api/order-log')
@login_required
def get_order_log():
    date_filter = request.args.get('date', '')
    symbol_filter = request.args.get('symbol', '')
    limit = request.args.get('limit', '100')  # Default to 100 rows

    try:
        limit = int(limit)
    except ValueError:
        limit = 100

    # Build the query based on filters
    query = "SELECT * FROM ORDER_LOG"
    params = []
    where_clauses = []

    if date_filter:
        where_clauses.append("DATE(timestamp) = %s")
        params.append(date_filter)

    if symbol_filter:
        where_clauses.append("symbol = %s")
        params.append(symbol_filter)

    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    # Add order by to get newest first
    query += " ORDER BY timestamp DESC"

    # Add limit
    if limit > 0:
        query += f" LIMIT {limit}"

    # Execute the query
    try:
        print(f"Executing query: {query} with params: {params}")
        df = get_data_from_db(query, tuple(params))
        print(f"Query returned {len(df)} rows")
        if df.empty:
            print("No data found in ORDER_LOG table")
            return jsonify([])
    except Exception as e:
        print(f"Error executing query: {e}")
        return jsonify({"error": str(e)})

    # Convert to list of dictionaries
    result = df.to_dict('records')

    # Format timestamp for display and handle NaN values
    for item in result:
        # Handle NaN values in all numeric fields
        for key, value in item.items():
            # Check if value is NaN or infinite
            if isinstance(value, float) and (pd.isna(value) or pd.isnull(value) or not np.isfinite(value)):
                item[key] = None

        # Format timestamp
        if 'timestamp' in item and item['timestamp']:
            try:
                # Convert to datetime if it's a string
                if isinstance(item['timestamp'], str):
                    dt = datetime.datetime.strptime(item['timestamp'], '%Y-%m-%d %H:%M:%S')
                else:
                    dt = item['timestamp']

                item['formatted_timestamp'] = dt.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                item['formatted_timestamp'] = str(item['timestamp'])

    return jsonify(result)

# API to get available dates in order log
@app.route('/api/order-log-dates')
@login_required
def get_order_log_dates():
    query = "SELECT DISTINCT DATE(timestamp) as date FROM ORDER_LOG ORDER BY date DESC"
    df = get_data_from_db(query)

    if df.empty:
        return jsonify([])

    # Convert dates to strings
    dates = [str(date) for date in df['date']]
    return jsonify(dates)

# API to get available symbols in order log
@app.route('/api/order-log-symbols')
@login_required
def get_order_log_symbols():
    query = "SELECT DISTINCT symbol FROM ORDER_LOG ORDER BY symbol"
    df = get_data_from_db(query)

    if df.empty:
        return jsonify([])

    symbols = df['symbol'].tolist()
    return jsonify(symbols)

# API to get all stocks from the database with pagination
@app.route('/api/stocks')
@login_required
def get_all_stocks():
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')

    # Calculate offset
    offset = (page - 1) * per_page

    # Get the refresh rate based on user's subscription
    subscription = Subscription.get_by_name(current_user.subscription_type)
    refresh_rate = 60  # Default refresh rate in seconds
    if subscription:
        try:
            features = subscription.features
            if isinstance(features, str):
                features = json.loads(features)
            refresh_rate = features.get('refresh_rate', 60)
        except:
            pass

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Count total records for pagination
        count_query = "SELECT COUNT(*) as total_count FROM livestocks"
        count_params = []

        # Add search condition if provided
        if search:
            count_query += " WHERE instrument_key LIKE %s"
            count_params.append(f'%{search}%')

        cursor.execute(count_query, count_params)
        result = cursor.fetchone()

        # Handle different ways the result might be returned
        if result:
            if isinstance(result, dict) and 'total_count' in result:
                total_records = result['total_count']
            elif isinstance(result, dict) and 'COUNT(*)' in result:
                total_records = result['COUNT(*)']
            elif hasattr(result, 'items'):  # It's a dict-like object
                total_records = list(result.values())[0]
            else:  # It's a tuple
                total_records = result[0]
        else:
            total_records = 0

        # Calculate total pages
        total_pages = (total_records + per_page - 1) // per_page

        try:
            # Build query for fetching stocks
            query = "SELECT * FROM livestocks"
            params = []

            # Add search condition if provided
            if search:
                query += " WHERE instrument_key LIKE %s"
                params.append(f'%{search}%')

            # Add order by and pagination
            query += " ORDER BY instrument_key LIMIT %s OFFSET %s"
            params.extend([per_page, offset])

            cursor.execute(query, params)

            stocks = []
            for stock in cursor.fetchall():
                # Get values with safe fallbacks
                stock_id = stock.get('id', 0) if isinstance(stock, dict) else stock[0]
                instrument_key = stock.get('instrument_key', '') if isinstance(stock, dict) else stock[1]
                instrument_token = stock.get('instrument_token', '') if isinstance(stock, dict) else stock[2]
                last_price = stock.get('last_price', 0) if isinstance(stock, dict) else stock[3]
                prev_close = stock.get('prev_close', 0) if isinstance(stock, dict) else stock[4]
                live_open = stock.get('live_open', 0) if isinstance(stock, dict) else stock[5]
                live_high = stock.get('live_high', 0) if isinstance(stock, dict) else stock[6]
                live_low = stock.get('live_low', 0) if isinstance(stock, dict) else stock[7]
                live_close = stock.get('live_close', 0) if isinstance(stock, dict) else stock[8]
                live_volume = stock.get('live_volume', 0) if isinstance(stock, dict) else stock[9]
                timestamp = stock.get('timestamp', None) if isinstance(stock, dict) else stock[10]

                # Calculate change percentage
                prev_close = prev_close if prev_close else 0
                last_price = last_price if last_price else 0
                change_percent = 0
                if prev_close > 0:
                    change_percent = ((last_price - prev_close) / prev_close) * 100

                # Format timestamp
                formatted_timestamp = None
                if timestamp:
                    try:
                        if hasattr(timestamp, 'strftime'):
                            formatted_timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            formatted_timestamp = str(timestamp)
                    except:
                        formatted_timestamp = None

                stocks.append({
                    'id': stock_id,
                    'instrument_key': instrument_key,
                    'instrument_token': instrument_token,
                    'last_price': last_price,
                    'prev_close': prev_close,
                    'change_percent': change_percent,
                    'live_open': live_open,
                    'live_high': live_high,
                    'live_low': live_low,
                    'live_close': live_close,
                    'live_volume': live_volume,
                    'timestamp': formatted_timestamp
                })
        except Exception as e:
            print(f"Error fetching stocks: {e}")
            stocks = []

        return jsonify({
            'stocks': stocks,
            'refresh_rate': refresh_rate,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_records': total_records,
                'total_pages': total_pages
            }
        })
    finally:
        cursor.close()
        conn.close()

# API to get all stocks from user's watchlists
@app.route('/api/user/all-stocks')
@login_required
def get_all_user_stocks():
    # Get all watchlists for the current user
    watchlists = Watchlist.get_by_user_id(current_user.id)

    # Get the refresh rate based on user's subscription
    subscription = Subscription.get_by_name(current_user.subscription_type)
    refresh_rate = 60  # Default refresh rate in seconds
    if subscription:
        try:
            features = subscription.features
            if isinstance(features, str):
                features = json.loads(features)
            refresh_rate = features.get('refresh_rate', 60)
        except:
            pass

    # Collect all unique stock keys from all watchlists
    all_stock_keys = set()
    for watchlist in watchlists:
        for stock in watchlist.stocks:
            all_stock_keys.add(stock.instrument_key)

    # Convert to list
    stock_keys = list(all_stock_keys)

    if not stock_keys:
        return jsonify({'stocks': [], 'refresh_rate': refresh_rate})

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Use placeholders for each stock key
        placeholders = ', '.join(['%s'] * len(stock_keys))
        query = f"""
        SELECT * FROM livestocks
        WHERE instrument_key IN ({placeholders})
        """

        cursor.execute(query, stock_keys)

        stocks = []
        for stock in cursor.fetchall():
            stocks.append({
                'id': stock['id'],
                'instrument_key': stock['instrument_key'],
                'instrument_token': stock['instrument_token'],
                'last_price': stock['last_price'],
                'prev_open': stock['prev_open'],
                'prev_high': stock['prev_high'],
                'prev_low': stock['prev_low'],
                'prev_close': stock['prev_close'],
                'prev_volume': stock['prev_volume'],
                'prev_ts': stock['prev_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['prev_ts'] else None,
                'live_open': stock['live_open'],
                'live_high': stock['live_high'],
                'live_low': stock['live_low'],
                'live_close': stock['live_close'],
                'live_volume': stock['live_volume'],
                'live_ts': stock['live_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['live_ts'] else None,
                'timestamp': stock['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if stock['timestamp'] else None
            })

        return jsonify({'stocks': stocks, 'refresh_rate': refresh_rate})
    finally:
        cursor.close()
        conn.close()

# API to get FII/DII data
@app.route('/api/fii-dii')
@login_required
def get_fii_dii_data():
    try:
        # Get parameters
        days = request.args.get('days', 30, type=int)

        conn = get_db_connection()
        cursor = conn.cursor()

        # Get FII data
        cursor.execute("""
            SELECT date, category, buy_value, sell_value, net_value
            FROM fii_dii_data
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL %s DAY)
            ORDER BY date DESC, category
        """, (days,))

        data = cursor.fetchall()

        # Process data for frontend
        fii_data = []
        dii_data = []
        dates = set()

        for row in data:
            date_str = row['date'].strftime('%Y-%m-%d')
            dates.add(date_str)

            if row['category'] == 'FII' or row['category'] == 'FII/FPI':
                fii_data.append({
                    'date': date_str,
                    'buy_value': float(row['buy_value']),
                    'sell_value': float(row['sell_value']),
                    'net_value': float(row['net_value'])
                })
            elif row['category'] == 'DII':
                dii_data.append({
                    'date': date_str,
                    'buy_value': float(row['buy_value']),
                    'sell_value': float(row['sell_value']),
                    'net_value': float(row['net_value'])
                })

        # Calculate summary statistics
        fii_summary = {
            'total_buy': sum(item['buy_value'] for item in fii_data),
            'total_sell': sum(item['sell_value'] for item in fii_data),
            'total_net': sum(item['net_value'] for item in fii_data),
            'avg_buy': sum(item['buy_value'] for item in fii_data) / len(fii_data) if fii_data else 0,
            'avg_sell': sum(item['sell_value'] for item in fii_data) / len(fii_data) if fii_data else 0,
            'avg_net': sum(item['net_value'] for item in fii_data) / len(fii_data) if fii_data else 0
        }

        dii_summary = {
            'total_buy': sum(item['buy_value'] for item in dii_data),
            'total_sell': sum(item['sell_value'] for item in dii_data),
            'total_net': sum(item['net_value'] for item in dii_data),
            'avg_buy': sum(item['buy_value'] for item in dii_data) / len(dii_data) if dii_data else 0,
            'avg_sell': sum(item['sell_value'] for item in dii_data) / len(dii_data) if dii_data else 0,
            'avg_net': sum(item['net_value'] for item in dii_data) / len(dii_data) if dii_data else 0
        }

        # Get unique dates sorted
        unique_dates = sorted(list(dates))

        return jsonify({
            'fii_data': fii_data,
            'dii_data': dii_data,
            'fii_summary': fii_summary,
            'dii_summary': dii_summary,
            'dates': unique_dates
        })
    except Exception as e:
        print(f"Error fetching FII/DII data: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# API endpoint for Advanced FII data
@app.route('/api/advance-fii', methods=['GET'])
@login_required
def get_advance_fii_data_api():
    """
    Get advanced FII data from the API

    Query parameters:
    - date: Optional date in YYYY-MM-DD format to filter data for a specific date

    Returns:
    - JSON with advanced FII data
    """
    date = request.args.get('date')
    data = advance_fii.get_advance_fii_data(date)
    return jsonify(data)

@app.route('/api/advance-fii/dates', methods=['GET'])
@login_required
def get_advance_fii_dates():
    """
    Get available dates for Advanced FII data

    Returns:
    - JSON with list of available dates
    """
    dates = advance_fii.get_available_dates()
    return jsonify(dates)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')

    # Create the FII/DII table if it doesn't exist
    fii.create_fii_dii_table()

    # Initialize the Advanced FII data cache
    advance_fii.init_cache()

    # Start the FII/DII data fetcher in a separate thread
    fii_thread = threading.Thread(target=fii.fetch_and_store_fii_dii_data_continuously, daemon=True)
    fii_thread.start()

    # Start the Advanced FII data background refresh thread
    advance_fii.start_background_refresh()

    app.run(debug=True, host='0.0.0.0', port=5000)
