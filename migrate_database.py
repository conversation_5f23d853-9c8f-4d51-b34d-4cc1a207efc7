import pymysql
import configparser
import os
import time

def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

def get_db_connection(database_name=None):
    mysql_config = get_mysql_config()

    # Use the specified database or the one from config
    db_name = database_name if database_name else mysql_config['database']

    conn = pymysql.connect(
        host=mysql_config['host'],
        port=int(mysql_config['port']),
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=db_name,
        cursorclass=pymysql.cursors.DictCursor
    )
    return conn

def get_all_tables(conn):
    cursor = conn.cursor()
    cursor.execute("SHOW TABLES")
    results = cursor.fetchall()
    tables = []

    # Handle different formats of result
    if results:
        # Get the first key in the dictionary (which should be the table name column)
        first_row = results[0]
        if first_row:
            table_key = list(first_row.keys())[0]
            tables = [row[table_key] for row in results]

    cursor.close()
    return tables

def get_create_table_statement(conn, table_name):
    cursor = conn.cursor()
    cursor.execute(f"SHOW CREATE TABLE {table_name}")
    create_statement = cursor.fetchone()['Create Table']
    cursor.close()
    return create_statement

def migrate_database():
    print("Starting database migration from option_Chain_data to option_chain_db...")

    # Connect to source database
    try:
        source_conn = get_db_connection('option_Chain_data')
        print("Connected to source database: option_Chain_data")
    except Exception as e:
        print(f"Error connecting to source database: {e}")
        return

    # Connect to target database
    try:
        target_conn = get_db_connection('option_chain_db')
        print("Connected to target database: option_chain_db")
    except Exception as e:
        print(f"Error connecting to target database: {e}")
        source_conn.close()
        return

    try:
        # Get all tables from source database
        source_tables = get_all_tables(source_conn)
        print(f"Found {len(source_tables)} tables in source database: {', '.join(source_tables)}")

        # Get existing tables in target database
        target_tables = get_all_tables(target_conn)
        print(f"Found {len(target_tables)} existing tables in target database: {', '.join(target_tables)}")

        # Process each table
        for table_name in source_tables:
            print(f"\nProcessing table: {table_name}")

            # Check if table already exists in target
            if table_name in target_tables:
                print(f"Table {table_name} already exists in target database.")
                user_input = input(f"Do you want to drop and recreate {table_name}? (y/n): ").lower()
                if user_input == 'y':
                    target_cursor = target_conn.cursor()
                    target_cursor.execute(f"DROP TABLE {table_name}")
                    target_conn.commit()
                    target_cursor.close()
                    print(f"Dropped table {table_name} from target database.")
                else:
                    print(f"Skipping table {table_name}.")
                    continue

            # Get CREATE TABLE statement
            create_statement = get_create_table_statement(source_conn, table_name)

            # Create table in target database
            target_cursor = target_conn.cursor()
            try:
                target_cursor.execute(create_statement)
                target_conn.commit()
                print(f"Created table {table_name} in target database.")
            except Exception as e:
                print(f"Error creating table {table_name}: {e}")
                continue
            finally:
                target_cursor.close()

            # Count rows in source table
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            row_count = source_cursor.fetchone()['count']
            source_cursor.close()

            if row_count == 0:
                print(f"Table {table_name} is empty. No data to migrate.")
                continue

            print(f"Migrating {row_count} rows from {table_name}...")

            # Get column names
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SHOW COLUMNS FROM {table_name}")
            columns = [column['Field'] for column in source_cursor.fetchall()]
            source_cursor.close()

            # Migrate data in batches
            batch_size = 10000
            for offset in range(0, row_count, batch_size):
                start_time = time.time()

                # Get batch of data from source
                source_cursor = source_conn.cursor()
                source_cursor.execute(f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}")
                rows = source_cursor.fetchall()
                source_cursor.close()

                if not rows:
                    break

                # Insert data into target
                target_cursor = target_conn.cursor()

                # Prepare column names and placeholders for INSERT statement
                column_names = ", ".join(columns)
                placeholders = ", ".join(["%s"] * len(columns))

                # Prepare batch insert query
                insert_query = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"

                # Prepare data for batch insert
                batch_data = []
                for row in rows:
                    row_data = [row[column] for column in columns]
                    batch_data.append(row_data)

                # Execute batch insert
                try:
                    target_cursor.executemany(insert_query, batch_data)
                    target_conn.commit()

                    elapsed_time = time.time() - start_time
                    print(f"Migrated batch of {len(rows)} rows (offset {offset}) in {elapsed_time:.2f} seconds.")
                except Exception as e:
                    print(f"Error inserting data into {table_name}: {e}")
                    break
                finally:
                    target_cursor.close()

            # Verify row count in target table
            target_cursor = target_conn.cursor()
            target_cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            target_row_count = target_cursor.fetchone()['count']
            target_cursor.close()

            print(f"Migration complete for {table_name}. Source: {row_count} rows, Target: {target_row_count} rows.")

            if row_count != target_row_count:
                print(f"WARNING: Row count mismatch for {table_name}!")

    except Exception as e:
        print(f"Error during migration: {e}")

    finally:
        # Close connections
        source_conn.close()
        target_conn.close()
        print("\nDatabase migration completed.")

if __name__ == "__main__":
    migrate_database()
