import pandas as pd
import requests
import pymysql
import configparser
import os
import time
import datetime
import json
import logging
from threading import Thread

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("fii_dii.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("FII_DII")

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Function to get a database connection
def get_db_connection():
    mysql_config = get_mysql_config()
    return pymysql.connect(
        host=mysql_config['host'],
        port=int(mysql_config['port']),
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=mysql_config['database'],
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

# Create FII_DII table if it doesn't exist
def create_fii_dii_table():
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'fii_dii_data'")
        table_exists = cursor.fetchone()

        if not table_exists:
            logger.info("Creating fii_dii_data table...")
            cursor.execute("""
            CREATE TABLE fii_dii_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE,
                category VARCHAR(10),
                buy_value DECIMAL(20, 2),
                sell_value DECIMAL(20, 2),
                net_value DECIMAL(20, 2),
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_date (date),
                INDEX idx_category (category)
            )
            """)
            conn.commit()
            logger.info("fii_dii_data table created successfully")
        else:
            logger.info("fii_dii_data table already exists")

    except Exception as e:
        logger.error(f"Error creating fii_dii_data table: {e}")
    finally:
        cursor.close()
        conn.close()

# Function to fetch FII/DII data from API
def fetch_fii_dii_data():
    # API URL
    url = "http://cmdatafeed.jmfonline.in/api/FIIDII-DailyEOD"

    try:
        # Send the GET request
        response = requests.get(url)

        # Check if the request was successful (status code 200)
        if response.status_code == 200:
            response_data = response.json()

            # Check if "data" exists and is not None
            data = response_data.get("data")

            if data:
                # Extract relevant fields from each record
                data_list = []
                for record in data:
                    data_list.append({
                        'category': record.get('Category'),
                        'date': record.get('FIIDate'),
                        'buy_value': record.get('BuyValue'),
                        'sell_value': record.get('SellValue'),
                        'net_value': record.get('NetValue')
                    })

                return data_list
            else:
                logger.warning("No data found in API response.")
                return []
        else:
            logger.error(f"Failed to fetch data. Status code: {response.status_code}")
            return []
    except Exception as e:
        logger.error(f"Error fetching FII/DII data: {e}")
        return []

# Function to store FII/DII data in database
def store_fii_dii_data(data_list):
    if not data_list:
        logger.warning("No data to store")
        return

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # First, delete existing data for the dates in the new data
        dates = set()
        for item in data_list:
            try:
                # Convert date string to date object
                date_str = item['date']
                try:
                    # Try different date formats
                    if 'T00:00:00' in date_str:
                        # Format: 2025-05-02T00:00:00
                        date_obj = datetime.datetime.strptime(date_str.split('T')[0], '%Y-%m-%d').date()
                    elif '-' in date_str and len(date_str.split('-')[0]) == 4:
                        # Format: 2025-05-02
                        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
                    else:
                        # Format: 02-May-2025
                        date_obj = datetime.datetime.strptime(date_str, '%d-%b-%Y').date()
                except Exception as e:
                    logger.error(f"Error parsing date {date_str}: {e}")
                    continue
                dates.add(date_obj.strftime('%Y-%m-%d'))
            except Exception as e:
                logger.error(f"Error parsing date {item['date']}: {e}")

        if dates:
            placeholders = ', '.join(['%s'] * len(dates))
            delete_query = f"DELETE FROM fii_dii_data WHERE date IN ({placeholders})"
            cursor.execute(delete_query, list(dates))
            conn.commit()

        # Insert new data
        for item in data_list:
            try:
                # Convert date string to date object
                date_str = item['date']
                try:
                    # Try different date formats
                    if 'T00:00:00' in date_str:
                        # Format: 2025-05-02T00:00:00
                        date_obj = datetime.datetime.strptime(date_str.split('T')[0], '%Y-%m-%d').date()
                    elif '-' in date_str and len(date_str.split('-')[0]) == 4:
                        # Format: 2025-05-02
                        date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
                    else:
                        # Format: 02-May-2025
                        date_obj = datetime.datetime.strptime(date_str, '%d-%b-%Y').date()
                except Exception as e:
                    logger.error(f"Error parsing date {date_str}: {e}")
                    continue

                # Insert data
                insert_query = """
                INSERT INTO fii_dii_data (date, category, buy_value, sell_value, net_value)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    date_obj,
                    item['category'],
                    item['buy_value'],
                    item['sell_value'],
                    item['net_value']
                ))
            except Exception as e:
                logger.error(f"Error inserting data: {e}")

        conn.commit()
        logger.info(f"Successfully stored {len(data_list)} FII/DII records")
    except Exception as e:
        logger.error(f"Error storing FII/DII data: {e}")
    finally:
        cursor.close()
        conn.close()

# Function to continuously fetch and store FII/DII data
def fetch_and_store_fii_dii_data_continuously():
    while True:
        try:
            logger.info("Fetching FII/DII data...")
            data = fetch_fii_dii_data()
            if data:
                logger.info(f"Fetched {len(data)} FII/DII records")
                store_fii_dii_data(data)
            else:
                logger.warning("No FII/DII data fetched")

            # Sleep for 15 minutes before fetching again
            time.sleep(15 * 60)
        except Exception as e:
            logger.error(f"Error in fetch_and_store_fii_dii_data_continuously: {e}")
            time.sleep(5)  # Wait a bit longer if there's an error

# Main function to run the FII/DII data fetcher
def main():
    # Create the FII/DII table if it doesn't exist
    create_fii_dii_table()

    # Start the continuous fetching and storing in a separate thread
    thread = Thread(target=fetch_and_store_fii_dii_data_continuously, daemon=True)
    thread.start()

    logger.info("FII/DII data fetcher started")

    # Keep the main thread running
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logger.info("FII/DII data fetcher stopped")

if __name__ == "__main__":
    main()