<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Option Chain Tik Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }
        .table th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 1;
        }
        .nav-tabs {
            margin-bottom: 20px;
        }
        .highlight-row {
            background-color: #e6f7ff !important;
        }
        .call-positive {
            background-color: rgba(0, 255, 0, 0.1);
        }
        .call-negative {
            background-color: rgba(255, 0, 0, 0.1);
        }
        .put-positive {
            background-color: rgba(0, 255, 0, 0.1);
        }
        .put-negative {
            background-color: rgba(255, 0, 0, 0.1);
        }
        .atm-strike {
            font-weight: bold;
            background-color: #fffde7;
        }
        #refreshStatus {
            font-size: 12px;
            color: #666;
        }
        .summary-card {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .summary-value {
            font-weight: bold;
            font-size: 1.2rem;
        }
        .summary-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .refresh-btn {
            cursor: pointer;
        }
        .date-selector {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">Option Chain Tik Analysis</h1>

        <div class="row mb-4">
            <div class="col-md-12 text-center">
                <div class="card">
                    <div class="card-body">
                        <h4>Welcome to our Stock Market Dashboard</h4>
                        <p>Track your favorite stocks in real-time with our powerful dashboard.</p>
                        <div class="mt-3">
                            <a href="{{ url_for('dashboard') }}" class="btn btn-primary me-2">Live Stocks Dashboard</a>
                            <a href="{{ url_for('subscriptions') }}" class="btn btn-outline-primary">Subscription Plans</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">Symbol</span>
                    <select id="symbolSelector" class="form-select">
                        <option value="">Loading symbols...</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-check form-switch mt-2">
                    <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
                    <label class="form-check-label" for="autoRefreshToggle">Auto-refresh (1 second)</label>
                    <span id="refreshStatus" class="ms-2"></span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button id="refreshBtn" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Data
                </button>
            </div>
        </div>

        <ul class="nav nav-tabs" id="dataTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab" aria-controls="live" aria-selected="true">Live Data</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="historical-tab" data-bs-toggle="tab" data-bs-target="#historical" type="button" role="tab" aria-controls="historical" aria-selected="false">Historical Data</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="oi-tab" data-bs-toggle="tab" data-bs-target="#oi" type="button" role="tab" aria-controls="oi" aria-selected="false">OI Data</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="oi-chart-tab" data-bs-toggle="tab" data-bs-target="#oi-chart" type="button" role="tab" aria-controls="oi-chart" aria-selected="false">OI Chart</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="orders-log-tab" data-bs-toggle="tab" data-bs-target="#orders-log" type="button" role="tab" aria-controls="orders-log" aria-selected="false">Orders Log</button>
            </li>
        </ul>

        <div class="tab-content" id="dataTabsContent">
            <!-- Live Data Tab -->
            <div class="tab-pane fade show active" id="live" role="tabpanel" aria-labelledby="live-tab">
                <div class="row" id="liveSummaryCards">
                    <!-- Summary cards will be inserted here -->
                </div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Option Chain</h5>
                        <span id="lastUpdated"></span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover" id="optionChainTable">
                                <thead>
                                    <tr>
                                        <th colspan="7" class="text-center bg-light">CALLS</th>
                                        <th class="text-center bg-warning">STRIKE</th>
                                        <th colspan="7" class="text-center bg-light">PUTS</th>
                                    </tr>
                                    <tr>
                                        <th>OI</th>
                                        <th>OI Chg</th>
                                        <th>Open</th>
                                        <th>High</th>
                                        <th>Low</th>
                                        <th>LTP</th>
                                        <th>Chg</th>
                                        <th class="text-center bg-warning">PRICE</th>
                                        <th>Chg</th>
                                        <th>LTP</th>
                                        <th>Low</th>
                                        <th>High</th>
                                        <th>Open</th>
                                        <th>OI Chg</th>
                                        <th>OI</th>
                                    </tr>
                                </thead>
                                <tbody id="optionChainBody">
                                    <!-- Option chain data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historical Data Tab -->
            <div class="tab-pane fade" id="historical" role="tabpanel" aria-labelledby="historical-tab">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">Date</span>
                            <select id="dateSelector" class="form-select date-selector">
                                <option value="">Select a date</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">Time</span>
                            <select id="timeSelector" class="form-select">
                                <option value="">Select a time</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row" id="historicalSummaryCards">
                    <!-- Historical summary cards will be inserted here -->
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Historical Option Chain</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover" id="historicalOptionChainTable">
                                <thead>
                                    <tr>
                                        <th colspan="7" class="text-center bg-light">CALLS</th>
                                        <th class="text-center bg-warning">STRIKE</th>
                                        <th colspan="7" class="text-center bg-light">PUTS</th>
                                    </tr>
                                    <tr>
                                        <th>OI</th>
                                        <th>OI Chg</th>
                                        <th>Open</th>
                                        <th>High</th>
                                        <th>Low</th>
                                        <th>LTP</th>
                                        <th>Chg</th>
                                        <th class="text-center bg-warning">PRICE</th>
                                        <th>Chg</th>
                                        <th>LTP</th>
                                        <th>Low</th>
                                        <th>High</th>
                                        <th>Open</th>
                                        <th>OI Chg</th>
                                        <th>OI</th>
                                    </tr>
                                </thead>
                                <tbody id="historicalOptionChainBody">
                                    <!-- Historical option chain data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- OI Data Tab -->
            <div class="tab-pane fade" id="oi" role="tabpanel" aria-labelledby="oi-tab">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Open Interest Data for All Symbols</h5>
                        <span id="oiLastUpdated"></span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover" id="oiDataTable">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>FUT OI</th>
                                        <th>FUT Change in OI</th>
                                        <th>Max Call Change in OI Strike</th>
                                        <th>Max Call Change in OI</th>
                                        <th>Max Call OI Strike</th>
                                        <th>Max Call OI</th>
                                        <th>SPOT LTP</th>
                                        <th>Max Put OI</th>
                                        <th>Max Put OI Strike</th>
                                        <th>Max Put Change in OI</th>
                                        <th>Max Put Change in OI Strike</th>
                                    </tr>
                                </thead>
                                <tbody id="oiDataBody">
                                    <!-- OI data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- OI Chart Tab -->
            <div class="tab-pane fade" id="oi-chart" role="tabpanel" aria-labelledby="oi-chart-tab">
                <div class="row mt-3 mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">Chart Type</span>
                            <select id="chartTypeSelector" class="form-select">
                                <option value="futOi">Futures OI</option>
                                <option value="futOiChange">Futures OI Change</option>
                                <option value="maxOi">Max OI</option>
                                <option value="maxOiChange">Max OI Change</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <span id="oiChartLastUpdated"></span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0" id="chartTitle">Open Interest Chart</h5>
                    </div>
                    <div class="card-body">
                        <div id="oiChart" style="height:60vh; width:100%"></div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Call vs Put OI Ratio</h5>
                            </div>
                            <div class="card-body">
                                <div id="pcrChart" style="height:30vh; width:100%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Max OI Strikes</h5>
                            </div>
                            <div class="card-body">
                                <div id="strikesChart" style="height:30vh; width:100%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">Symbol Comparison</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">Metric</span>
                                    <select id="comparisonMetricSelector" class="form-select">
                                        <option value="FUT_OI">Futures OI</option>
                                        <option value="FUT_Change_in_OI">Futures OI Change</option>
                                        <option value="Spot_LTP">Spot LTP</option>
                                        <option value="Max_Call_OI">Max Call OI</option>
                                        <option value="Max_Put_OI">Max Put OI</option>
                                        <option value="Max_Call_OI_Strike">Max Call OI Strike</option>
                                        <option value="Max_Put_OI_Strike">Max Put OI Strike</option>
                                        <option value="Max_Call_Change_in_OI">Max Call OI Change</option>
                                        <option value="Max_Put_Change_in_OI">Max Put OI Change</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div id="comparisonChart" style="height:40vh; width:100%"></div>
                    </div>
                </div>
            </div>

            <!-- Orders Log Tab -->
            <div class="tab-pane fade" id="orders-log" role="tabpanel" aria-labelledby="orders-log-tab">
                <div class="row mt-3 mb-3">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">Date</span>
                            <select id="orderLogDateSelector" class="form-select">
                                <option value="">All Dates</option>
                                <!-- Dates will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">Symbol</span>
                            <select id="orderLogSymbolSelector" class="form-select">
                                <option value="">All Symbols</option>
                                <!-- Symbols will be loaded dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">Rows</span>
                            <select id="orderLogLimitSelector" class="form-select">
                                <option value="100">100</option>
                                <option value="200">200</option>
                                <option value="500">500</option>
                                <option value="1000">1000</option>
                                <option value="0">All</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button id="orderLogRefreshBtn" class="btn btn-primary me-2">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                        <span id="orderLogLastUpdated"></span>
                    </div>
                </div>

                <div id="orderLogLoading" class="position-absolute top-50 start-50 translate-middle d-none" style="z-index: 1000;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Trading Orders Log</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover" id="orderLogTable">
                                <thead>
                                    <tr>
                                        <th>Timestamp</th>
                                        <th>Symbol</th>
                                        <th>Order Type</th>
                                        <th>Option Type</th>
                                        <th>Strike</th>
                                        <th>Spot Price</th>
                                        <th>Option Price</th>
                                        <th>P&L</th>
                                        <th>Support</th>
                                        <th>Resistance</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                    </tr>
                                </thead>
                                <tbody id="orderLogBody">
                                    <!-- Order log data will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let autoRefresh = true;
        let refreshInterval;
        let orderLogRefreshInterval;
        let currentSymbol = '';
        let historicalData = {};
        let currentOrderLogDate = '';
        let currentOrderLogSymbol = '';

        // DOM elements
        const symbolSelector = document.getElementById('symbolSelector');
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        const refreshBtn = document.getElementById('refreshBtn');
        const refreshStatus = document.getElementById('refreshStatus');
        const lastUpdated = document.getElementById('lastUpdated');
        const liveSummaryCards = document.getElementById('liveSummaryCards');
        const optionChainBody = document.getElementById('optionChainBody');
        const dateSelector = document.getElementById('dateSelector');
        const timeSelector = document.getElementById('timeSelector');
        const historicalSummaryCards = document.getElementById('historicalSummaryCards');
        const historicalOptionChainBody = document.getElementById('historicalOptionChainBody');
        const orderLogDateSelector = document.getElementById('orderLogDateSelector');
        const orderLogSymbolSelector = document.getElementById('orderLogSymbolSelector');
        const orderLogLimitSelector = document.getElementById('orderLogLimitSelector');
        const orderLogRefreshBtn = document.getElementById('orderLogRefreshBtn');
        const orderLogBody = document.getElementById('orderLogBody');
        const orderLogLastUpdated = document.getElementById('orderLogLastUpdated');
        const orderLogLoading = document.getElementById('orderLogLoading');

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadSymbols();

            // Load OI data on page load
            loadOIData();

            // Initialize Orders Log tab data
            loadOrderLogDates();
            loadOrderLogSymbols();
            loadOrderLogData();

            // Clean up intervals when page is unloaded
            window.addEventListener('beforeunload', function() {
                stopAutoRefresh();
                stopOrderLogAutoRefresh();
            });

            // Event listeners
            symbolSelector.addEventListener('change', function() {
                currentSymbol = this.value;
                if (currentSymbol) {
                    loadLiveData();
                    loadDates();
                    startAutoRefresh();
                }
            });

            autoRefreshToggle.addEventListener('change', function() {
                autoRefresh = this.checked;
                if (autoRefresh) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });

            refreshBtn.addEventListener('click', function() {
                if (currentSymbol) {
                    loadLiveData();
                }
            });

            dateSelector.addEventListener('change', function() {
                if (currentSymbol && this.value) {
                    loadHistoricalData();
                }
            });

            timeSelector.addEventListener('change', function() {
                if (currentSymbol && dateSelector.value && this.value) {
                    displayHistoricalData(this.value);
                }
            });

            // Tab change event
            document.getElementById('dataTabs').addEventListener('shown.bs.tab', function(e) {
                // First stop any auto-refresh for order log
                stopOrderLogAutoRefresh();

                if (e.target.id === 'historical-tab') {
                    if (currentSymbol) {
                        loadDates();
                    }
                } else if (e.target.id === 'oi-tab') {
                    loadOIData();
                } else if (e.target.id === 'oi-chart-tab') {
                    // Initialize charts if needed
                    if (!oiChart || !pcrChart || !strikesChart) {
                        initCharts();
                    }
                    // Load data for charts
                    loadOIData();
                } else if (e.target.id === 'orders-log-tab') {
                    // Load order log data and start auto-refresh
                    loadOrderLogDates();
                    loadOrderLogSymbols();
                    loadOrderLogData();
                    startOrderLogAutoRefresh();
                }
            });

            // Order log date selector event
            orderLogDateSelector.addEventListener('change', function() {
                currentOrderLogDate = this.value;
                loadOrderLogData();
            });

            // Order log symbol selector event
            orderLogSymbolSelector.addEventListener('change', function() {
                currentOrderLogSymbol = this.value;
                loadOrderLogData();
            });

            // Order log limit selector event
            orderLogLimitSelector.addEventListener('change', function() {
                loadOrderLogData();
            });

            // Order log refresh button event
            orderLogRefreshBtn.addEventListener('click', function() {
                loadOrderLogData();
            });

            // Chart type selector event
            document.getElementById('chartTypeSelector').addEventListener('change', function() {
                // Reload OI data to update charts with the new type
                loadOIData();
            });

            // Comparison metric selector event
            document.getElementById('comparisonMetricSelector').addEventListener('change', function() {
                // Reload OI data to update charts with the new metric
                loadOIData();
            });
        });

        // Load available symbols
        function loadSymbols() {
            fetch('/api/symbols')
                .then(response => response.json())
                .then(data => {
                    symbolSelector.innerHTML = '';
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select a symbol';
                    symbolSelector.appendChild(defaultOption);

                    data.forEach(symbol => {
                        const option = document.createElement('option');
                        option.value = symbol;
                        option.textContent = symbol;
                        symbolSelector.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading symbols:', error));
        }

        // Load live data for the selected symbol
        function loadLiveData() {
            if (!currentSymbol) return;

            fetch(`/api/latest/${currentSymbol}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error(data.error);
                        return;
                    }

                    displayLiveData(data);
                    lastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
                    refreshStatus.textContent = 'Data refreshed';
                    setTimeout(() => { refreshStatus.textContent = ''; }, 2000);
                })
                .catch(error => {
                    console.error('Error loading live data:', error);
                    refreshStatus.textContent = 'Error refreshing data';
                });
        }

        // Display live data
        function displayLiveData(data) {
            // Display summary cards
            displaySummaryCards(data.summary, liveSummaryCards);

            // Display option chain
            optionChainBody.innerHTML = '';

            // Sort by strike price
            data.option_chain.sort((a, b) => parseFloat(a.strike) - parseFloat(b.strike));

            // Find ATM strike (closest to spot price)
            const spotPrice = parseFloat(data.summary.Spot_LTP);
            let atmStrike = data.option_chain.length > 0 ? data.option_chain[0].strike : 0;
            let minDiff = data.option_chain.length > 0 ? Math.abs(parseFloat(data.option_chain[0].strike) - spotPrice) : Infinity;

            data.option_chain.forEach(option => {
                const strike = parseFloat(option.strike);
                const diff = Math.abs(strike - spotPrice);
                if (diff < minDiff) {
                    minDiff = diff;
                    atmStrike = option.strike;
                }
            });

            // Create table rows
            data.option_chain.forEach(option => {
                const row = document.createElement('tr');

                // Check if this is the ATM strike
                if (option.strike === atmStrike) {
                    row.classList.add('atm-strike');
                }

                // Format numbers with commas for better readability
                const formatNumber = (num) => {
                    if (num === null || num === undefined || num === '') return '0';
                    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                };

                // Call side
                const ceOI = formatNumber(option.data.CE_OI);
                const ceOIChange = formatNumber(option.data.CE_OI_Change);
                const ceOpen = option.data.CE_Open ? option.data.CE_Open.toString() : '0';
                const ceHigh = option.data.CE_High ? option.data.CE_High.toString() : '0';
                const ceLow = option.data.CE_Low ? option.data.CE_Low.toString() : '0';
                const ceLTP = option.data.CE_LTP ? option.data.CE_LTP.toString() : '0';
                const ceLTPChange = option.data.CE_LTP_Change ? option.data.CE_LTP_Change.toString() : '0';

                // Put side
                const peOI = formatNumber(option.data.PE_OI);
                const peOIChange = formatNumber(option.data.PE_OI_Change);
                const peOpen = option.data.PE_Open ? option.data.PE_Open.toString() : '0';
                const peHigh = option.data.PE_High ? option.data.PE_High.toString() : '0';
                const peLow = option.data.PE_Low ? option.data.PE_Low.toString() : '0';
                const peLTP = option.data.PE_LTP ? option.data.PE_LTP.toString() : '0';
                const peLTPChange = option.data.PE_LTP_Change ? option.data.PE_LTP_Change.toString() : '0';

                // Add classes for positive/negative changes
                const ceOIChangeClass = parseFloat(option.data.CE_OI_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const ceLTPChangeClass = parseFloat(option.data.CE_LTP_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const peOIChangeClass = parseFloat(option.data.PE_OI_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const peLTPChangeClass = parseFloat(option.data.PE_LTP_Change || 0) >= 0 ? 'text-success' : 'text-danger';

                row.innerHTML = `
                    <td>${ceOI}</td>
                    <td class="${ceOIChangeClass}">${ceOIChange}</td>
                    <td>${ceOpen}</td>
                    <td>${ceHigh}</td>
                    <td>${ceLow}</td>
                    <td>${ceLTP}</td>
                    <td class="${ceLTPChangeClass}">${ceLTPChange}</td>
                    <td class="text-center bg-warning">${option.strike}</td>
                    <td class="${peLTPChangeClass}">${peLTPChange}</td>
                    <td>${peLTP}</td>
                    <td>${peLow}</td>
                    <td>${peHigh}</td>
                    <td>${peOpen}</td>
                    <td class="${peOIChangeClass}">${peOIChange}</td>
                    <td>${peOI}</td>
                `;

                optionChainBody.appendChild(row);
            });

            // Show timestamp from the data
            if (data.timestamp) {
                lastUpdated.textContent = `Data as of: ${data.timestamp}`;
            }
        }

        // Display summary cards
        function displaySummaryCards(summary, container) {
            container.innerHTML = '';

            // Format numbers with commas for better readability
            const formatNumber = (num) => {
                if (num === null || num === undefined || num === '') return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            };

            // Create cards for important metrics
            const metrics = [
                { label: 'Spot LTP', value: summary.Spot_LTP, change: summary.Spot_LTP_Change },
                { label: 'FUT LTP', value: summary.FUT_LTP, change: summary.FUT_LTP_Change },
                { label: 'VIX', value: summary.VIX_LTP, change: summary.VIX_LTP_Change },
                { label: 'PCR', value: summary.PCR },
                { label: 'Max Pain', value: summary.Max_Pain_Strike },
                { label: 'Total Call OI', value: summary.Total_Call_OI, change: summary.Total_Call_Change_in_OI },
                { label: 'Total Put OI', value: summary.Total_Put_OI, change: summary.Total_Put_Change_in_OI },
                { label: 'Max Call OI', value: summary.Max_Call_OI, strike: summary.Max_Call_OI_Strike },
                { label: 'Max Put OI', value: summary.Max_Put_OI, strike: summary.Max_Put_OI_Strike }
            ];

            metrics.forEach(metric => {
                const col = document.createElement('div');
                col.className = 'col-md-4 mb-3';

                // Format the value
                const formattedValue = formatNumber(metric.value);

                let changeHtml = '';
                if (metric.change !== undefined) {
                    const changeValue = parseFloat(metric.change || 0);
                    const changeClass = changeValue >= 0 ? 'text-success' : 'text-danger';
                    const changeSign = changeValue >= 0 ? '+' : '';
                    changeHtml = `<span class="${changeClass}">(${changeSign}${formatNumber(metric.change)})</span>`;
                }

                let strikeHtml = '';
                if (metric.strike !== undefined) {
                    strikeHtml = `<div class="small text-muted">Strike: ${metric.strike || 'N/A'}</div>`;
                }

                col.innerHTML = `
                    <div class="card summary-card h-100">
                        <div class="card-body">
                            <div class="summary-label">${metric.label}</div>
                            <div class="summary-value">${formattedValue} ${changeHtml}</div>
                            ${strikeHtml}
                        </div>
                    </div>
                `;

                container.appendChild(col);
            });
        }

        // Load available dates for historical data
        function loadDates() {
            if (!currentSymbol) return;

            fetch(`/api/dates?symbol=${currentSymbol}`)
                .then(response => response.json())
                .then(data => {
                    dateSelector.innerHTML = '';
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select a date';
                    dateSelector.appendChild(defaultOption);

                    data.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        dateSelector.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading dates:', error));
        }

        // Load historical data for the selected date
        function loadHistoricalData() {
            if (!currentSymbol || !dateSelector.value) return;

            fetch(`/api/historical?symbol=${currentSymbol}&date=${dateSelector.value}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error(data.error);
                        return;
                    }

                    historicalData = data;

                    // Populate time selector
                    timeSelector.innerHTML = '';
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select a time';
                    timeSelector.appendChild(defaultOption);

                    // Use timestamps from the API response if available
                    let timestamps = data.timestamps || [];

                    // If no timestamps in the response, extract from option_chain
                    if (timestamps.length === 0 && data.option_chain) {
                        timestamps = Object.keys(data.option_chain).sort().reverse();
                    }

                    timestamps.forEach(timestamp => {
                        const option = document.createElement('option');
                        // Extract just the time part if it's a full timestamp
                        const timeParts = timestamp.split(' ');
                        const timeValue = timeParts.length > 1 ? timestamp : timestamp;
                        const displayTime = timeParts.length > 1 ? timeParts[1] : timestamp;

                        option.value = timeValue;
                        option.textContent = displayTime;
                        timeSelector.appendChild(option);
                    });

                    // Select the first time by default
                    if (timeSelector.options.length > 1) {
                        timeSelector.selectedIndex = 1;
                        displayHistoricalData(timeSelector.value);
                    }
                })
                .catch(error => console.error('Error loading historical data:', error));
        }

        // Display historical data for the selected time
        function displayHistoricalData(time) {
            if (!historicalData || !historicalData.summary || !historicalData.option_chain[time]) return;

            // Find the matching summary data for this time
            const summaryData = historicalData.summary.find(s => s.timestamp.includes(time)) || historicalData.summary[0];

            // Display summary cards
            displaySummaryCards(summaryData, historicalSummaryCards);

            // Display option chain
            historicalOptionChainBody.innerHTML = '';

            // Sort by strike price
            const optionChain = historicalData.option_chain[time].sort((a, b) => parseFloat(a.strike) - parseFloat(b.strike));

            // Find ATM strike (closest to spot price)
            const spotPrice = parseFloat(summaryData.Spot_LTP);
            let atmStrike = optionChain.length > 0 ? optionChain[0].strike : 0;
            let minDiff = optionChain.length > 0 ? Math.abs(parseFloat(optionChain[0].strike) - spotPrice) : Infinity;

            optionChain.forEach(option => {
                const strike = parseFloat(option.strike);
                const diff = Math.abs(strike - spotPrice);
                if (diff < minDiff) {
                    minDiff = diff;
                    atmStrike = option.strike;
                }
            });

            // Create table rows
            optionChain.forEach(option => {
                const row = document.createElement('tr');

                // Check if this is the ATM strike
                if (option.strike === atmStrike) {
                    row.classList.add('atm-strike');
                }

                // Format numbers with commas for better readability
                const formatNumber = (num) => {
                    if (num === null || num === undefined || num === '') return '0';
                    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                };

                // Call side
                const ceOI = formatNumber(option.data.CE_OI);
                const ceOIChange = formatNumber(option.data.CE_OI_Change);
                const ceOpen = option.data.CE_Open ? option.data.CE_Open.toString() : '0';
                const ceHigh = option.data.CE_High ? option.data.CE_High.toString() : '0';
                const ceLow = option.data.CE_Low ? option.data.CE_Low.toString() : '0';
                const ceLTP = option.data.CE_LTP ? option.data.CE_LTP.toString() : '0';
                const ceLTPChange = option.data.CE_LTP_Change ? option.data.CE_LTP_Change.toString() : '0';

                // Put side
                const peOI = formatNumber(option.data.PE_OI);
                const peOIChange = formatNumber(option.data.PE_OI_Change);
                const peOpen = option.data.PE_Open ? option.data.PE_Open.toString() : '0';
                const peHigh = option.data.PE_High ? option.data.PE_High.toString() : '0';
                const peLow = option.data.PE_Low ? option.data.PE_Low.toString() : '0';
                const peLTP = option.data.PE_LTP ? option.data.PE_LTP.toString() : '0';
                const peLTPChange = option.data.PE_LTP_Change ? option.data.PE_LTP_Change.toString() : '0';

                // Add classes for positive/negative changes
                const ceOIChangeClass = parseFloat(option.data.CE_OI_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const ceLTPChangeClass = parseFloat(option.data.CE_LTP_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const peOIChangeClass = parseFloat(option.data.PE_OI_Change || 0) >= 0 ? 'text-success' : 'text-danger';
                const peLTPChangeClass = parseFloat(option.data.PE_LTP_Change || 0) >= 0 ? 'text-success' : 'text-danger';

                row.innerHTML = `
                    <td>${ceOI}</td>
                    <td class="${ceOIChangeClass}">${ceOIChange}</td>
                    <td>${ceOpen}</td>
                    <td>${ceHigh}</td>
                    <td>${ceLow}</td>
                    <td>${ceLTP}</td>
                    <td class="${ceLTPChangeClass}">${ceLTPChange}</td>
                    <td class="text-center bg-warning">${option.strike}</td>
                    <td class="${peLTPChangeClass}">${peLTPChange}</td>
                    <td>${peLTP}</td>
                    <td>${peLow}</td>
                    <td>${peHigh}</td>
                    <td>${peOpen}</td>
                    <td class="${peOIChangeClass}">${peOIChange}</td>
                    <td>${peOI}</td>
                `;

                historicalOptionChainBody.appendChild(row);
            });
        }

        // Auto-refresh functionality
        function startAutoRefresh() {
            if (!autoRefresh || !currentSymbol) return;

            // Clear any existing interval
            stopAutoRefresh();

            // Set new interval (1 second)
            refreshInterval = setInterval(() => {
                loadLiveData();
            }, 1000);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // Start order log auto-refresh
        function startOrderLogAutoRefresh() {
            stopOrderLogAutoRefresh(); // Clear any existing interval

            // Initial load
            loadOrderLogData();

            // Set up interval for smooth refreshes
            orderLogRefreshInterval = setInterval(() => {
                // Use requestAnimationFrame for smoother updates
                requestAnimationFrame(() => {
                    loadOrderLogData();
                });
            }, 1000); // Refresh every second
        }

        // Stop order log auto-refresh
        function stopOrderLogAutoRefresh() {
            if (orderLogRefreshInterval) {
                clearInterval(orderLogRefreshInterval);
                orderLogRefreshInterval = null;
            }
        }

        // Load order log dates
        function loadOrderLogDates() {
            fetch('/api/order-log-dates')
                .then(response => response.json())
                .then(dates => {
                    orderLogDateSelector.innerHTML = '<option value="">All Dates</option>';

                    dates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        orderLogDateSelector.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading order log dates:', error));
        }

        // Load order log symbols
        function loadOrderLogSymbols() {
            fetch('/api/order-log-symbols')
                .then(response => response.json())
                .then(symbols => {
                    orderLogSymbolSelector.innerHTML = '<option value="">All Symbols</option>';

                    symbols.forEach(symbol => {
                        const option = document.createElement('option');
                        option.value = symbol;
                        option.textContent = symbol;
                        orderLogSymbolSelector.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading order log symbols:', error));
        }

        // Load order log data
        function loadOrderLogData() {
            // Only show loading indicator for initial load or manual refresh
            const isManualRefresh = event && event.type === 'click' && event.target === orderLogRefreshBtn;
            const isInitialLoad = !orderLogBody.hasChildNodes();

            if (isManualRefresh || isInitialLoad) {
                orderLogLoading.classList.remove('d-none');
            }

            let url = '/api/order-log';
            const params = [];

            if (currentOrderLogDate) {
                params.push(`date=${currentOrderLogDate}`);
            }

            if (currentOrderLogSymbol) {
                params.push(`symbol=${currentOrderLogSymbol}`);
            }

            // Get the limit from the selector
            const limit = orderLogLimitSelector.value;
            if (limit !== '0') {  // 0 means 'All'
                params.push(`limit=${limit}`);
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            // Add a timestamp parameter to prevent caching during auto-refresh
            if (!isManualRefresh && !isInitialLoad) {
                const cacheBuster = new Date().getTime();
                url += (url.includes('?') ? '&' : '?') + `_=${cacheBuster}`;
            }

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        console.error('API returned error:', data.error);
                        return;
                    }

                    // Only update the DOM if there are changes
                    const currentData = JSON.stringify(data);
                    if (!orderLogBody._lastData || orderLogBody._lastData !== currentData) {
                        displayOrderLogData(data);
                        orderLogBody._lastData = currentData;
                    }

                    // Update timestamp without flashing
                    const now = new Date().toLocaleTimeString();
                    if (orderLogLastUpdated.textContent !== `Last updated: ${now}`) {
                        orderLogLastUpdated.textContent = `Last updated: ${now}`;
                    }
                })
                .catch(error => {
                    console.error('Error loading order log data:', error);
                    // Only show error if it's a manual refresh or initial load
                    if (isManualRefresh || isInitialLoad) {
                        orderLogBody.innerHTML = '';
                        const row = document.createElement('tr');
                        const cell = document.createElement('td');
                        cell.colSpan = 12;
                        cell.textContent = `Error loading data: ${error.message}`;
                        cell.className = 'text-center text-danger';
                        row.appendChild(cell);
                        orderLogBody.appendChild(row);
                    }
                })
                .finally(() => {
                    // Hide loading indicator
                    orderLogLoading.classList.add('d-none');
                });
        }

        // Display order log data
        function displayOrderLogData(data) {
            // Create a document fragment to minimize DOM operations
            const fragment = document.createDocumentFragment();

            if (data.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 12;
                cell.textContent = 'No order log data available';
                cell.className = 'text-center';
                row.appendChild(cell);
                fragment.appendChild(row);

                // Replace the entire content at once
                orderLogBody.innerHTML = '';
                orderLogBody.appendChild(fragment);
                return;
            }

            // Create all rows in the fragment

            data.forEach(order => {
                const row = document.createElement('tr');

                // Add class based on order type
                if (order.order_type === 'BUY') {
                    row.className = 'table-success';
                } else if (order.order_type === 'SELL') {
                    row.className = 'table-danger';
                }

                // Add timestamp
                const timestampCell = document.createElement('td');
                timestampCell.textContent = order.formatted_timestamp || order.timestamp;
                row.appendChild(timestampCell);

                // Add symbol
                const symbolCell = document.createElement('td');
                symbolCell.textContent = order.symbol;
                row.appendChild(symbolCell);

                // Add order type
                const orderTypeCell = document.createElement('td');
                orderTypeCell.textContent = order.order_type;
                orderTypeCell.className = order.order_type === 'BUY' ? 'text-success' : 'text-danger';
                row.appendChild(orderTypeCell);

                // Add option type
                const optionTypeCell = document.createElement('td');
                optionTypeCell.textContent = order.option_type;
                row.appendChild(optionTypeCell);

                // Add strike
                const strikeCell = document.createElement('td');
                strikeCell.textContent = order.strike || '-';
                row.appendChild(strikeCell);

                // Add spot price
                const spotPriceCell = document.createElement('td');
                if (order.spot_price !== null && order.spot_price !== undefined) {
                    try {
                        const spotValue = parseFloat(order.spot_price);
                        if (!isNaN(spotValue)) {
                            spotPriceCell.textContent = spotValue.toFixed(2);
                        } else {
                            spotPriceCell.textContent = '-';
                        }
                    } catch (e) {
                        spotPriceCell.textContent = '-';
                    }
                } else {
                    spotPriceCell.textContent = '-';
                }
                row.appendChild(spotPriceCell);

                // Add option price
                const optionPriceCell = document.createElement('td');
                if (order.option_price !== null && order.option_price !== undefined) {
                    try {
                        const optionValue = parseFloat(order.option_price);
                        if (!isNaN(optionValue)) {
                            optionPriceCell.textContent = optionValue.toFixed(2);
                        } else {
                            optionPriceCell.textContent = '-';
                        }
                    } catch (e) {
                        optionPriceCell.textContent = '-';
                    }
                } else {
                    optionPriceCell.textContent = '-';
                }
                row.appendChild(optionPriceCell);

                // Add P&L
                const pnlCell = document.createElement('td');
                if (order.pnl !== null && order.pnl !== undefined) {
                    try {
                        const pnlValue = parseFloat(order.pnl);
                        if (!isNaN(pnlValue)) {
                            pnlCell.textContent = pnlValue.toFixed(2);
                            if (pnlValue > 0) {
                                pnlCell.className = 'text-success';
                            } else if (pnlValue < 0) {
                                pnlCell.className = 'text-danger';
                            }
                        } else {
                            pnlCell.textContent = '-';
                        }
                    } catch (e) {
                        console.error('Error parsing PNL value:', e);
                        pnlCell.textContent = '-';
                    }
                } else {
                    pnlCell.textContent = '-';
                }
                row.appendChild(pnlCell);

                // Add support
                const supportCell = document.createElement('td');
                if (order.support !== null && order.support !== undefined) {
                    try {
                        const supportValue = parseFloat(order.support);
                        if (!isNaN(supportValue)) {
                            supportCell.textContent = supportValue.toFixed(2);
                        } else {
                            supportCell.textContent = '-';
                        }
                    } catch (e) {
                        supportCell.textContent = '-';
                    }
                } else {
                    supportCell.textContent = '-';
                }
                row.appendChild(supportCell);

                // Add resistance
                const resistanceCell = document.createElement('td');
                if (order.resistance !== null && order.resistance !== undefined) {
                    try {
                        const resistanceValue = parseFloat(order.resistance);
                        if (!isNaN(resistanceValue)) {
                            resistanceCell.textContent = resistanceValue.toFixed(2);
                        } else {
                            resistanceCell.textContent = '-';
                        }
                    } catch (e) {
                        resistanceCell.textContent = '-';
                    }
                } else {
                    resistanceCell.textContent = '-';
                }
                row.appendChild(resistanceCell);

                // Add status
                const statusCell = document.createElement('td');
                statusCell.textContent = order.status;
                row.appendChild(statusCell);

                // Add reason
                const reasonCell = document.createElement('td');
                reasonCell.textContent = order.reason;
                row.appendChild(reasonCell);

                fragment.appendChild(row);
            });

            // Replace the entire content at once for smoother updates
            orderLogBody.innerHTML = '';
            orderLogBody.appendChild(fragment);
        }

        // Load OI data for all symbols
        function loadOIData() {
            fetch('/api/oi-data')
                .then(response => response.json())
                .then(data => {
                    displayOIData(data);
                    document.getElementById('oiLastUpdated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

                    // Update charts if we're on the OI Chart tab
                    if (document.getElementById('oi-chart-tab').classList.contains('active')) {
                        updateCharts(data);
                    }
                })
                .catch(error => console.error('Error loading OI data:', error));
        }

        // Display OI data
        function displayOIData(data) {
            const oiDataBody = document.getElementById('oiDataBody');
            oiDataBody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');

                // Format numbers with commas for better readability
                const formatNumber = (num) => {
                    if (num === null || num === undefined) return '0';
                    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                };

                // Add classes for positive/negative changes
                const getChangeClass = (value) => {
                    if (value === null || value === undefined) return '';
                    return parseFloat(value) >= 0 ? 'text-success' : 'text-danger';
                };

                row.innerHTML = `
                    <td><strong>${item.symbol}</strong></td>
                    <td>${formatNumber(item.FUT_OI)}</td>
                    <td class="${getChangeClass(item.FUT_Change_in_OI)}">${formatNumber(item.FUT_Change_in_OI)}</td>
                    <td>${item.Max_Call_Change_in_OI_Strike}</td>
                    <td class="${getChangeClass(item.Max_Call_Change_in_OI)}">${formatNumber(item.Max_Call_Change_in_OI)}</td>
                    <td>${item.Max_Call_OI_Strike}</td>
                    <td>${formatNumber(item.Max_Call_OI)}</td>
                    <td>${formatNumber(item.Spot_LTP)}</td>
                    <td>${formatNumber(item.Max_Put_OI)}</td>
                    <td>${item.Max_Put_OI_Strike}</td>
                    <td class="${getChangeClass(item.Max_Put_Change_in_OI)}">${formatNumber(item.Max_Put_Change_in_OI)}</td>
                    <td>${item.Max_Put_Change_in_OI_Strike}</td>
                `;

                oiDataBody.appendChild(row);
            });

            // Set up auto-refresh for OI data
            setTimeout(loadOIData, 1000); // Refresh every second
        }

        // Chart objects
        let oiChart = null;
        let pcrChart = null;
        let strikesChart = null;
        let comparisonChart = null;

        // Initialize charts
        function initCharts() {
            // Main OI Chart
            const oiChartOptions = {
                chart: {
                    type: 'bar',
                    height: '100%',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        animateGradually: {
                            enabled: true,
                            delay: 150
                        },
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    toolbar: {
                        show: false
                    }
                },
                series: [],
                xaxis: {
                    categories: []
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return val.toLocaleString();
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toLocaleString();
                        }
                    }
                },
                legend: {
                    position: 'top'
                },
                title: {
                    text: 'Open Interest Chart',
                    align: 'center'
                },
                noData: {
                    text: 'Loading data...'
                }
            };

            oiChart = new ApexCharts(document.getElementById('oiChart'), oiChartOptions);
            oiChart.render();

            // PCR Chart
            const pcrChartOptions = {
                chart: {
                    type: 'line',
                    height: '100%',
                    animations: {
                        enabled: true,
                        easing: 'smooth',
                        speed: 800,
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    toolbar: {
                        show: false
                    }
                },
                series: [{
                    name: 'Put/Call Ratio',
                    data: []
                }],
                xaxis: {
                    categories: []
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: 'vertical',
                        shadeIntensity: 0.5,
                        opacityFrom: 0.7,
                        opacityTo: 0.2
                    }
                },
                markers: {
                    size: 5,
                    hover: {
                        size: 7
                    }
                },
                title: {
                    text: 'Call vs Put OI Ratio',
                    align: 'center'
                },
                noData: {
                    text: 'Loading data...'
                }
            };

            pcrChart = new ApexCharts(document.getElementById('pcrChart'), pcrChartOptions);
            pcrChart.render();

            // Strikes Chart
            const strikesChartOptions = {
                chart: {
                    type: 'bar',
                    height: '100%',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    toolbar: {
                        show: false
                    }
                },
                series: [
                    {
                        name: 'Call OI Strike',
                        data: []
                    },
                    {
                        name: 'Put OI Strike',
                        data: []
                    }
                ],
                xaxis: {
                    categories: []
                },
                colors: ['#2E93fA', '#FF4560'],
                title: {
                    text: 'Max OI Strikes',
                    align: 'center'
                },
                noData: {
                    text: 'Loading data...'
                }
            };

            strikesChart = new ApexCharts(document.getElementById('strikesChart'), strikesChartOptions);
            strikesChart.render();

            // Comparison Chart
            const comparisonChartOptions = {
                chart: {
                    type: 'bar',
                    height: '100%',
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    },
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '70%',
                        distributed: false
                    }
                },
                series: [],
                xaxis: {
                    categories: []
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return val.toLocaleString();
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toLocaleString();
                        }
                    }
                },
                legend: {
                    position: 'top'
                },
                title: {
                    text: 'Symbol Comparison',
                    align: 'center'
                },
                noData: {
                    text: 'Loading data...'
                }
            };

            comparisonChart = new ApexCharts(document.getElementById('comparisonChart'), comparisonChartOptions);
            comparisonChart.render();
        }

        // Update charts with new data
        function updateCharts(data) {
            if (!oiChart || !pcrChart || !strikesChart || !comparisonChart) {
                initCharts();
            }

            const chartType = document.getElementById('chartTypeSelector').value;
            const comparisonMetric = document.getElementById('comparisonMetricSelector').value;
            document.getElementById('oiChartLastUpdated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

            // Sort data by symbol for consistent ordering
            data.sort((a, b) => a.symbol.localeCompare(b.symbol));

            // Extract labels (symbols)
            const labels = data.map(item => item.symbol);

            // Update main chart based on selected type
            updateMainChart(chartType, labels, data);

            // Update PCR chart
            updatePCRChart(labels, data);

            // Update strikes chart
            updateStrikesChart(labels, data);

            // Update comparison chart
            updateComparisonChart(comparisonMetric, labels, data);
        }

        // Update the main OI chart based on selected type
        function updateMainChart(chartType, labels, data) {
            let series = [];
            let chartTitle = '';

            switch(chartType) {
                case 'futOi':
                    chartTitle = 'Futures Open Interest';
                    series = [{
                        name: 'Futures OI',
                        data: data.map(item => item.FUT_OI || 0)
                    }];
                    break;

                case 'futOiChange':
                    chartTitle = 'Futures Open Interest Change';
                    series = [{
                        name: 'Futures OI Change',
                        data: data.map(item => item.FUT_Change_in_OI || 0)
                    }];
                    break;



                case 'maxOi':
                    chartTitle = 'Max Call vs Put Open Interest';
                    series = [
                        {
                            name: 'Max Call OI',
                            data: data.map(item => item.Max_Call_OI || 0)
                        },
                        {
                            name: 'Max Put OI',
                            data: data.map(item => item.Max_Put_OI || 0)
                        }
                    ];
                    break;

                case 'maxOiChange':
                    chartTitle = 'Max Call vs Put Open Interest Change';
                    series = [
                        {
                            name: 'Max Call OI Change',
                            data: data.map(item => item.Max_Call_Change_in_OI || 0)
                        },
                        {
                            name: 'Max Put OI Change',
                            data: data.map(item => item.Max_Put_Change_in_OI || 0)
                        }
                    ];
                    break;
            }

            // Update chart title
            oiChart.updateOptions({
                title: {
                    text: chartTitle
                },
                xaxis: {
                    categories: labels
                }
            });

            // Update chart data
            oiChart.updateSeries(series);
        }

        // Update the PCR chart
        function updatePCRChart(labels, data) {
            // Calculate PCR values
            const pcrValues = data.map(item => {
                const callOI = parseFloat(item.Max_Call_OI || 0);
                const putOI = parseFloat(item.Max_Put_OI || 0);
                return callOI > 0 ? parseFloat((putOI / callOI).toFixed(2)) : 0;
            });

            pcrChart.updateOptions({
                xaxis: {
                    categories: labels
                }
            });

            pcrChart.updateSeries([{
                name: 'Put/Call Ratio',
                data: pcrValues
            }]);
        }

        // Update the strikes chart
        function updateStrikesChart(labels, data) {
            // Get strike values
            const callStrikes = data.map(item => parseFloat(item.Max_Call_OI_Strike || 0));
            const putStrikes = data.map(item => parseFloat(item.Max_Put_OI_Strike || 0));

            strikesChart.updateOptions({
                xaxis: {
                    categories: labels
                }
            });

            strikesChart.updateSeries([
                {
                    name: 'Call OI Strike',
                    data: callStrikes
                },
                {
                    name: 'Put OI Strike',
                    data: putStrikes
                }
            ]);
        }

        // Update the comparison chart
        function updateComparisonChart(metric, labels, data) {
            // Get the display name for the metric
            const metricDisplayNames = {
                'FUT_OI': 'Futures OI',
                'FUT_Change_in_OI': 'Futures OI Change',
                'Spot_LTP': 'Spot LTP',
                'Max_Call_OI': 'Max Call OI',
                'Max_Put_OI': 'Max Put OI',
                'Max_Call_OI_Strike': 'Max Call OI Strike',
                'Max_Put_OI_Strike': 'Max Put OI Strike',
                'Max_Call_Change_in_OI': 'Max Call OI Change',
                'Max_Put_Change_in_OI': 'Max Put OI Change'
            };

            const metricName = metricDisplayNames[metric] || metric;

            // Get the values for the selected metric
            const metricValues = data.map(item => parseFloat(item[metric] || 0));

            // Create a series for each symbol
            const series = labels.map((symbol, index) => ({
                name: symbol,
                data: [metricValues[index]]
            }));

            comparisonChart.updateOptions({
                title: {
                    text: `${metricName} Comparison`
                },
                xaxis: {
                    categories: [metricName]
                }
            });

            comparisonChart.updateSeries(series);
        }
    </script>
</body>
</html>
