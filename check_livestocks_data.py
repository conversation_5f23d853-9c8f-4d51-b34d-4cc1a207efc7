import pymysql
import configparser
import os

def get_mysql_config():
    """Get MySQL connection details from config file or create if not exists"""
    config = configparser.ConfigParser()

    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

def check_livestocks_data():
    """Check the data in the livestocks table"""
    mysql_config = get_mysql_config()

    try:
        # Connect to MySQL
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Create a cursor
        cursor = conn.cursor()

        # Check if the table exists
        cursor.execute("SHOW TABLES LIKE 'livestocks'")
        if not cursor.fetchone():
            print("The 'livestocks' table does not exist.")
            return

        # Get the count of records
        cursor.execute("SELECT COUNT(*) FROM livestocks")
        count = cursor.fetchone()[0]
        print(f"Total records in livestocks table: {count}")

        # Get the most recent records
        cursor.execute("""
        SELECT
            id,
            FROM_UNIXTIME(timestamp/1000) as time_recorded,
            instrument_key,
            last_price,
            live_open,
            live_high,
            live_low,
            live_close,
            live_volume
        FROM livestocks
        ORDER BY id DESC
        LIMIT 10
        """)

        records = cursor.fetchall()

        print("\nMost recent records:")
        print("ID | Time Recorded | Instrument Key | Last Price | Open | High | Low | Close | Volume")
        print("-" * 100)

        for record in records:
            print(f"{record[0]} | {record[1]} | {record[2]} | {record[3]} | {record[4]} | {record[5]} | {record[6]} | {record[7]} | {record[8]}")

        # Close connection
        conn.close()

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_livestocks_data()
