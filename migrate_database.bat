@echo off
echo Database Migration Tool - option_Chain_data to option_chain_db
echo ============================================================

set HOST=localhost
set USER=root
set PASSWORD=vinayak123
set SOURCE_DB=option_Chain_data
set TARGET_DB=option_chain_db

echo Starting database migration from %SOURCE_DB% to %TARGET_DB%...

REM Check if MySQL is in PATH
where mysql >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo MySQL command not found in PATH.
    echo Please add MySQL bin directory to your PATH or specify the full path below:
    set /p MYSQL_PATH="Enter MySQL bin directory path (e.g., C:\Program Files\MySQL\MySQL Server 8.0\bin): "
    set MYSQL="%MYSQL_PATH%\mysql.exe"
    set MYSQLDUMP="%MYSQL_PATH%\mysqldump.exe"
) else (
    set MYSQL=mysql
    set MYSQLDUMP=mysqldump
)

REM Check if target database exists, create if not
%MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %TARGET_DB%;"
if %ERRORLEVEL% NEQ 0 (
    echo Failed to create target database.
    goto :error
)

REM Get list of tables from source database
echo Getting list of tables from source database...
%MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "SHOW TABLES FROM %SOURCE_DB%;" -N > tables.txt
if %ERRORLEVEL% NEQ 0 (
    echo Failed to get tables from source database.
    goto :error
)

REM Process each table
for /f "tokens=*" %%t in (tables.txt) do (
    echo.
    echo Processing table: %%t
    
    REM Check if table exists in target database
    %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='%TARGET_DB%' AND table_name='%%t';" -N > table_exists.txt
    set /p TABLE_EXISTS=<table_exists.txt
    
    if "%TABLE_EXISTS%"=="1" (
        echo Table %%t already exists in target database.
        set /p RESPONSE="Do you want to drop and recreate %%t? (y/n): "
        if /i "%RESPONSE%"=="y" (
            %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% %TARGET_DB% -e "DROP TABLE %%t;"
            if %ERRORLEVEL% NEQ 0 (
                echo Failed to drop table %%t.
                goto :error
            )
            echo Dropped table %%t from target database.
        ) else (
            echo Skipping table %%t.
            goto :next_table
        )
    )
    
    REM Create table structure in target database
    echo Creating table structure for %%t in target database...
    %MYSQLDUMP% -h%HOST% -u%USER% -p%PASSWORD% --no-data %SOURCE_DB% %%t > table_structure.sql
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to dump table structure for %%t.
        goto :error
    )
    
    %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% %TARGET_DB% < table_structure.sql
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to create table %%t in target database.
        goto :error
    )
    echo Created table %%t in target database.
    
    REM Count rows in source table
    %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "SELECT COUNT(*) FROM %SOURCE_DB%.%%t;" -N > row_count.txt
    set /p ROW_COUNT=<row_count.txt
    
    if "%ROW_COUNT%"=="0" (
        echo Table %%t is empty. No data to migrate.
        goto :next_table
    )
    
    echo Migrating %ROW_COUNT% rows from %%t...
    
    REM Migrate data
    %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "INSERT INTO %TARGET_DB%.%%t SELECT * FROM %SOURCE_DB%.%%t;"
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to migrate data for table %%t.
        goto :error
    )
    
    REM Verify row count in target table
    %MYSQL% -h%HOST% -u%USER% -p%PASSWORD% -e "SELECT COUNT(*) FROM %TARGET_DB%.%%t;" -N > target_row_count.txt
    set /p TARGET_ROW_COUNT=<target_row_count.txt
    
    echo Migration complete for %%t. Source: %ROW_COUNT% rows, Target: %TARGET_ROW_COUNT% rows.
    
    if not "%ROW_COUNT%"=="%TARGET_ROW_COUNT%" (
        echo WARNING: Row count mismatch for %%t!
    )
    
    :next_table
)

echo.
echo Database migration completed.

REM Clean up temporary files
del tables.txt
del table_exists.txt
del table_structure.sql
del row_count.txt
del target_row_count.txt

goto :end

:error
echo Migration failed.

:end
pause
