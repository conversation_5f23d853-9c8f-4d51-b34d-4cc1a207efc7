{% extends "base.html" %}

{% block title %}Edit Subscription{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Edit Subscription: {{ subscription.name|capitalize }}</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin_edit_subscription', subscription_id=subscription.id) }}">
                    {{ form.hidden_tag() }}
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control") }}
                                {% for error in form.name.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.price.label(class="form-label") }}
                                {{ form.price(class="form-control") }}
                                {% for error in form.price.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.duration_days.label(class="form-label") }}
                        {{ form.duration_days(class="form-control") }}
                        {% for error in form.duration_days.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.features.label(class="form-label") }}
                        {{ form.features(class="form-control", rows=10) }}
                        <small class="text-muted">Enter features as a JSON object, e.g. {"max_watchlists": 3, "max_stocks_per_watchlist": 20, "refresh_rate": 30}</small>
                        {% for error in form.features.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin_subscriptions') }}" class="btn btn-secondary">Cancel</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
