{% extends "base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Admin Dashboard</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Users</h5>
                                <h2 class="display-4">{{ user_count }}</h2>
                                <a href="{{ url_for('admin_users') }}" class="btn btn-primary mt-3">Manage Users</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Active Subscriptions</h5>
                                <h2 class="display-4">{{ active_subscriptions }}</h2>
                                <a href="{{ url_for('admin_subscriptions') }}" class="btn btn-primary mt-3">Manage Subscriptions</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Watchlists</h5>
                                <h2 class="display-4">{{ watchlist_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Subscription Distribution</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Subscription Type</th>
                                <th>Number of Users</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sub in subscription_distribution %}
                            <tr>
                                <td>{{ sub.subscription_type|capitalize }}</td>
                                <td>{{ sub.count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
