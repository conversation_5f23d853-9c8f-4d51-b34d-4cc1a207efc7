import requests

url = "https://oxide.sensibull.com/v1/compute/cache/fii_dii_daily"

payload = {}
headers = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'en-GB,en;q=0.9',
  'origin': 'https://web.sensibull.com',
  'priority': 'u=1, i',
  'referer': 'https://web.sensibull.com/',
  'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
  'Cookie': '_cfuvid=SRy88MMt4RCLvHHe1PY8d2QxMzKEk7SNisI4Y9pF.iw-1746266801947-0.0.1.1-604800000'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
