{% extends "base.html" %}

{% block title %}Option Chain Tik Analysis{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<style>
    body {
        padding-top: 60px;
        background-color: #f5f5f5;
    }
    .container {
        max-width: 1400px;
    }
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    .table th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    .nav-tabs {
        margin-bottom: 20px;
    }
    .highlight-row {
        background-color: #e6f7ff !important;
    }
    .call-positive {
        background-color: rgba(0, 255, 0, 0.1);
    }
    .call-negative {
        background-color: rgba(255, 0, 0, 0.1);
    }
    .put-positive {
        background-color: rgba(0, 255, 0, 0.1);
    }
    .put-negative {
        background-color: rgba(255, 0, 0, 0.1);
    }
    .atm-strike {
        font-weight: bold;
        background-color: #fffde7;
    }
    #refreshStatus {
        font-size: 12px;
        color: #666;
    }
    .summary-card {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
    }
    .summary-value {
        font-weight: bold;
        font-size: 1.2rem;
    }
    .summary-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .refresh-btn {
        cursor: pointer;
    }
    .date-selector {
        max-width: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12 text-center">
        <div class="card">
            <div class="card-body">
                <h4>Welcome to our Stock Market Dashboard</h4>
                <p>Track your favorite stocks in real-time with our powerful dashboard.</p>
                <div class="mt-3">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary me-2">Live Stocks Dashboard</a>
                    <a href="{{ url_for('subscriptions') }}" class="btn btn-outline-primary">Subscription Plans</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-4">
        <div class="input-group">
            <span class="input-group-text">Symbol</span>
            <select id="symbolSelector" class="form-select">
                <option value="">Loading symbols...</option>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-check form-switch mt-2">
            <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
            <label class="form-check-label" for="autoRefreshToggle">Auto-refresh (1 second)</label>
            <span id="refreshStatus" class="ms-2"></span>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <button id="refreshBtn" class="btn btn-outline-primary">
            <i class="bi bi-arrow-clockwise"></i> Refresh Data
        </button>
    </div>
</div>

<ul class="nav nav-tabs" id="dataTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab" aria-controls="live" aria-selected="true">Live Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="historical-tab" data-bs-toggle="tab" data-bs-target="#historical" type="button" role="tab" aria-controls="historical" aria-selected="false">Historical Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="oi-tab" data-bs-toggle="tab" data-bs-target="#oi" type="button" role="tab" aria-controls="oi" aria-selected="false">OI Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="oi-chart-tab" data-bs-toggle="tab" data-bs-target="#oi-chart" type="button" role="tab" aria-controls="oi-chart" aria-selected="false">OI Chart</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="orders-log-tab" data-bs-toggle="tab" data-bs-target="#orders-log" type="button" role="tab" aria-controls="orders-log" aria-selected="false">Orders Log</button>
    </li>
</ul>

<div class="tab-content" id="dataTabsContent">
    <!-- Live Data Tab -->
    <div class="tab-pane fade show active" id="live" role="tabpanel" aria-labelledby="live-tab">
        <div class="row" id="liveSummaryCards">
            <!-- Summary cards will be inserted here -->
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Option Chain</h5>
                <span id="lastUpdated"></span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="optionChainTable">
                        <thead>
                            <tr>
                                <th colspan="7" class="text-center bg-light">CALLS</th>
                                <th class="text-center bg-warning">STRIKE</th>
                                <th colspan="7" class="text-center bg-light">PUTS</th>
                            </tr>
                            <tr>
                                <th>OI</th>
                                <th>OI Chg</th>
                                <th>Open</th>
                                <th>High</th>
                                <th>Low</th>
                                <th>LTP</th>
                                <th>Chg</th>
                                <th class="text-center bg-warning">PRICE</th>
                                <th>Chg</th>
                                <th>LTP</th>
                                <th>Low</th>
                                <th>High</th>
                                <th>Open</th>
                                <th>OI Chg</th>
                                <th>OI</th>
                            </tr>
                        </thead>
                        <tbody id="optionChainBody">
                            <!-- Option chain data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Historical Data Tab -->
    <div class="tab-pane fade" id="historical" role="tabpanel" aria-labelledby="historical-tab">
        <!-- Historical data content -->
    </div>

    <!-- OI Data Tab -->
    <div class="tab-pane fade" id="oi" role="tabpanel" aria-labelledby="oi-tab">
        <!-- OI data content -->
    </div>

    <!-- OI Chart Tab -->
    <div class="tab-pane fade" id="oi-chart" role="tabpanel" aria-labelledby="oi-chart-tab">
        <!-- OI chart content -->
    </div>

    <!-- Orders Log Tab -->
    <div class="tab-pane fade" id="orders-log" role="tabpanel" aria-labelledby="orders-log-tab">
        <!-- Orders log content -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let autoRefresh = true;
    let refreshInterval;
    let orderLogRefreshInterval;
    let currentSymbol = '';
    let historicalData = {};
    let currentOrderLogDate = '';
    let currentOrderLogSymbol = '';

    // DOM elements
    const symbolSelector = document.getElementById('symbolSelector');
    const autoRefreshToggle = document.getElementById('autoRefreshToggle');
    const refreshBtn = document.getElementById('refreshBtn');
    const refreshStatus = document.getElementById('refreshStatus');
    const lastUpdated = document.getElementById('lastUpdated');
    const liveSummaryCards = document.getElementById('liveSummaryCards');
    const optionChainBody = document.getElementById('optionChainBody');

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        loadSymbols();

        // Load OI data on page load
        loadOIData();

        // Clean up intervals when page is unloaded
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
            stopOrderLogAutoRefresh();
        });

        // Event listeners
        symbolSelector.addEventListener('change', function() {
            currentSymbol = this.value;
            if (currentSymbol) {
                loadLiveData();
                loadDates();
                startAutoRefresh();
            }
        });

        autoRefreshToggle.addEventListener('change', function() {
            autoRefresh = this.checked;
            if (autoRefresh) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        refreshBtn.addEventListener('click', function() {
            if (currentSymbol) {
                loadLiveData();
            }
        });
    });

    // Function to load symbols
    function loadSymbols() {
        fetch('/api/symbols')
            .then(response => response.json())
            .then(data => {
                symbolSelector.innerHTML = '<option value="">Select a symbol</option>';
                data.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    symbolSelector.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading symbols:', error));
    }

    // Function to load live data
    function loadLiveData() {
        if (!currentSymbol) return;

        fetch(`/api/latest/${currentSymbol}`)
            .then(response => response.json())
            .then(data => {
                // Update UI with live data
                updateLiveData(data);
            })
            .catch(error => console.error('Error loading live data:', error));
    }

    // Function to update live data in the UI
    function updateLiveData(data) {
        // Implementation details would go here
        lastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }

    // Function to start auto-refresh
    function startAutoRefresh() {
        stopAutoRefresh();
        if (autoRefresh && currentSymbol) {
            refreshInterval = setInterval(() => {
                loadLiveData();
                refreshStatus.textContent = `Last refresh: ${new Date().toLocaleTimeString()}`;
            }, 1000);
        }
    }

    // Function to stop auto-refresh
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    // Function to load OI data
    function loadOIData() {
        fetch('/api/oi-data')
            .then(response => response.json())
            .then(data => {
                // Update UI with OI data
            })
            .catch(error => console.error('Error loading OI data:', error));
    }

    // Function to stop order log auto-refresh
    function stopOrderLogAutoRefresh() {
        if (orderLogRefreshInterval) {
            clearInterval(orderLogRefreshInterval);
            orderLogRefreshInterval = null;
        }
    }
</script>
{% endblock %}
