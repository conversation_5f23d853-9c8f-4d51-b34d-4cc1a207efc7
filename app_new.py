import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, session
from flask_login import Lo<PERSON><PERSON>ana<PERSON>, login_user, logout_user, login_required, current_user
import datetime
import json
import os
import configparser
import pymysql
from sqlalchemy import create_engine
from werkzeug.urls import url_parse

# Import our models and forms
from models import User, Subscription, Watchlist, WatchlistItem, create_tables, get_db_connection
from forms import LoginForm, RegistrationForm, SubscriptionForm, WatchlistForm, AddStockForm

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'  # Change this to a random secret key
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(days=7)
app.config['REGISTERED_ENDPOINTS'] = []  # No admin endpoints in this file

# Initialize Flask-Login
login_manager = LoginManager(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.get_by_id(user_id)

# Ensure all required tables exist
create_tables()

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Function to get data from the database
def get_data_from_db(query, params=()):
    mysql_config = get_mysql_config()

    try:
        # Create connection
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Convert SQLite-style placeholders (?) to MySQL-style placeholders (%s)
        query = query.replace('?', '%s')

        # Execute query
        df = pd.read_sql_query(query, conn, params=params)

        # Close connection
        conn.close()

        return df
    except Exception as e:
        print(f"Database error: {e}")
        return pd.DataFrame()

# Routes for authentication
@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.get_by_username(form.username.data)
        if user is None or not user.check_password(form.password.data):
            flash('Invalid username or password', 'danger')
            return redirect(url_for('login'))

        login_user(user, remember=form.remember_me.data)
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('dashboard')
        return redirect(next_page)

    return render_template('auth/login.html', title='Sign In', form=form)

@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            role='basic',
            subscription_type='free',
            subscription_expiry=datetime.datetime.now() + datetime.timedelta(days=30)  # Free 30-day trial
        )
        user.set_password(form.password.data)
        if user.save():
            flash('Congratulations, you are now a registered user!', 'success')
            return redirect(url_for('login'))
        else:
            flash('Registration failed. Please try again.', 'danger')

    return render_template('auth/register.html', title='Register', form=form)

# Routes for main pages
@app.route('/')
def index():
    # Show the simplified homepage
    return render_template('index_simplified.html', title='Home')

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's watchlists
    watchlists = Watchlist.get_by_user_id(current_user.id)

    # Get subscription info
    subscription = Subscription.get_by_name(current_user.subscription_type)

    # Create a new watchlist form
    watchlist_form = WatchlistForm()

    # Create a form for adding stocks to watchlist
    add_stock_form = AddStockForm()

    return render_template(
        'dashboard/index.html',
        title='Dashboard',
        watchlists=watchlists,
        subscription=subscription,
        watchlist_form=watchlist_form,
        add_stock_form=add_stock_form
    )

@app.route('/option-data')
@login_required
def option_data():
    """
    Option Chain Analysis Dashboard
    Shows live data, historical data, OI data, OI charts, and orders log
    """
    return render_template('dashboard/option_data.html', title='Option Data Dashboard')

@app.route('/subscriptions', methods=['GET', 'POST'])
@login_required
def subscriptions():
    form = SubscriptionForm()
    subscriptions = Subscription.get_all()

    if form.validate_on_submit():
        subscription = Subscription.get_by_name(form.plan.data)
        if subscription:
            # In a real application, you would process payment here
            # For now, we'll just update the user's subscription
            user = current_user
            user.subscription_type = subscription.name
            user.subscription_expiry = datetime.datetime.now() + datetime.timedelta(days=subscription.duration_days)
            if user.save():
                flash(f'Successfully subscribed to {subscription.name} plan!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('Failed to update subscription. Please try again.', 'danger')
        else:
            flash('Invalid subscription plan selected.', 'danger')

    return render_template('dashboard/subscriptions.html', title='Subscriptions', form=form, subscriptions=subscriptions)

# Routes for watchlist management
@app.route('/watchlist/create', methods=['POST'])
@login_required
def create_watchlist():
    form = WatchlistForm()
    if form.validate_on_submit():
        # Check if user has reached their watchlist limit
        watchlists = Watchlist.get_by_user_id(current_user.id)
        subscription = Subscription.get_by_name(current_user.subscription_type)

        if subscription and len(watchlists) >= json.loads(subscription.features).get('max_watchlists', 1):
            flash(f'You have reached your limit of {json.loads(subscription.features).get("max_watchlists", 1)} watchlists. Upgrade your subscription to create more.', 'warning')
            return redirect(url_for('dashboard'))

        watchlist = Watchlist(
            user_id=current_user.id,
            name=form.name.data
        )
        if watchlist.save():
            flash(f'Watchlist "{form.name.data}" created successfully!', 'success')
        else:
            flash('Failed to create watchlist. Please try again.', 'danger')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{getattr(form, field).label.text}: {error}', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/delete', methods=['POST'])
@login_required
def delete_watchlist(watchlist_id):
    watchlist = Watchlist.get_by_id(watchlist_id)
    if watchlist and watchlist.user_id == current_user.id:
        if watchlist.delete():
            flash(f'Watchlist "{watchlist.name}" deleted successfully!', 'success')
        else:
            flash('Failed to delete watchlist. Please try again.', 'danger')
    else:
        flash('Watchlist not found or you do not have permission to delete it.', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/add_stock', methods=['POST'])
@login_required
def add_stock_to_watchlist(watchlist_id):
    form = AddStockForm()
    if form.validate_on_submit():
        watchlist = Watchlist.get_by_id(watchlist_id)
        if watchlist and watchlist.user_id == current_user.id:
            # Check if user has reached their stocks per watchlist limit
            subscription = Subscription.get_by_name(current_user.subscription_type)
            if subscription and len(watchlist.stocks) >= json.loads(subscription.features).get('max_stocks_per_watchlist', 5):
                flash(f'You have reached your limit of {json.loads(subscription.features).get("max_stocks_per_watchlist", 5)} stocks per watchlist. Upgrade your subscription to add more.', 'warning')
                return redirect(url_for('dashboard'))

            # Check if stock already exists in watchlist
            for stock in watchlist.stocks:
                if stock.instrument_key == form.instrument_key.data:
                    flash(f'Stock {form.instrument_key.data} is already in your watchlist.', 'warning')
                    return redirect(url_for('dashboard'))

            # Check if the stock exists in the livestocks table
            conn = get_db_connection()
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT instrument_key FROM livestocks WHERE instrument_key = %s", (form.instrument_key.data,))
                if not cursor.fetchone():
                    flash(f'Stock {form.instrument_key.data} not found in our database.', 'danger')
                    return redirect(url_for('dashboard'))
            finally:
                cursor.close()
                conn.close()

            # Add stock to watchlist
            watchlist_item = WatchlistItem(
                watchlist_id=watchlist.id,
                instrument_key=form.instrument_key.data
            )
            if watchlist_item.save():
                flash(f'Stock {form.instrument_key.data} added to watchlist successfully!', 'success')
            else:
                flash('Failed to add stock to watchlist. Please try again.', 'danger')
        else:
            flash('Watchlist not found or you do not have permission to modify it.', 'danger')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{getattr(form, field).label.text}: {error}', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/watchlist/<int:watchlist_id>/remove_stock/<int:stock_id>', methods=['POST'])
@login_required
def remove_stock_from_watchlist(watchlist_id, stock_id):
    watchlist = Watchlist.get_by_id(watchlist_id)
    if watchlist and watchlist.user_id == current_user.id:
        for stock in watchlist.stocks:
            if stock.id == stock_id:
                if stock.delete():
                    flash('Stock removed from watchlist successfully!', 'success')
                else:
                    flash('Failed to remove stock from watchlist. Please try again.', 'danger')
                break
        else:
            flash('Stock not found in watchlist.', 'danger')
    else:
        flash('Watchlist not found or you do not have permission to modify it.', 'danger')

    return redirect(url_for('dashboard'))

# API routes for live stock data
@app.route('/api/stocks/search', methods=['GET'])
@login_required
def search_stocks():
    query = request.args.get('q', '')
    if not query or len(query) < 2:
        return jsonify([])

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("""
        SELECT instrument_key, instrument_token, last_price
        FROM livestocks
        WHERE instrument_key LIKE %s
        LIMIT 20
        """, (f'%{query}%',))

        stocks = []
        for stock in cursor.fetchall():
            stocks.append({
                'instrument_key': stock['instrument_key'],
                'instrument_token': stock['instrument_token'],
                'last_price': stock['last_price']
            })

        return jsonify(stocks)
    finally:
        cursor.close()
        conn.close()

@app.route('/api/watchlist/<int:watchlist_id>/stocks', methods=['GET'])
@login_required
def get_watchlist_stocks(watchlist_id):
    watchlist = Watchlist.get_by_id(watchlist_id)
    if not watchlist or watchlist.user_id != current_user.id:
        return jsonify({'error': 'Watchlist not found or access denied'}), 404

    # Get the refresh rate based on user's subscription
    subscription = Subscription.get_by_name(current_user.subscription_type)
    refresh_rate = 60  # Default refresh rate in seconds
    if subscription:
        refresh_rate = json.loads(subscription.features).get('refresh_rate', 60)

    # Get live data for all stocks in the watchlist
    stock_keys = [stock.instrument_key for stock in watchlist.stocks]
    if not stock_keys:
        return jsonify({'stocks': [], 'refresh_rate': refresh_rate})

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Use placeholders for each stock key
        placeholders = ', '.join(['%s'] * len(stock_keys))
        query = f"""
        SELECT * FROM livestocks
        WHERE instrument_key IN ({placeholders})
        """

        cursor.execute(query, stock_keys)

        stocks = []
        for stock in cursor.fetchall():
            stocks.append({
                'id': stock['id'],
                'instrument_key': stock['instrument_key'],
                'instrument_token': stock['instrument_token'],
                'last_price': stock['last_price'],
                'prev_open': stock['prev_open'],
                'prev_high': stock['prev_high'],
                'prev_low': stock['prev_low'],
                'prev_close': stock['prev_close'],
                'prev_volume': stock['prev_volume'],
                'prev_ts': stock['prev_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['prev_ts'] else None,
                'live_open': stock['live_open'],
                'live_high': stock['live_high'],
                'live_low': stock['live_low'],
                'live_close': stock['live_close'],
                'live_volume': stock['live_volume'],
                'live_ts': stock['live_ts'].strftime('%Y-%m-%d %H:%M:%S') if stock['live_ts'] else None,
                'timestamp': stock['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if stock['timestamp'] else None
            })

        return jsonify({'stocks': stocks, 'refresh_rate': refresh_rate})
    finally:
        cursor.close()
        conn.close()

# Keep the original API routes from the existing app.py
# Function to extract symbol from timestamp
def extract_symbol(timestamp):
    parts = timestamp.split()
    if len(parts) > 3 and parts[3] == "symbol":  # Format: "YYYY-MM-DD HH:MM:SS Symbol symbol"
        return parts[2]
    return "Unknown"

# Function to extract symbols from database
def extract_symbols_from_db():
    # Try to get symbols directly from the live tables first
    query = "SELECT DISTINCT symbol FROM option_data_need_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Try to get symbols from option_data_live table
    query = "SELECT DISTINCT symbol FROM option_data_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from historical tables
    query = "SELECT DISTINCT symbol FROM option_data_need WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from option_data table
    query = "SELECT DISTINCT symbol FROM option_data WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Common symbols to look for in timestamps
    common_symbols = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'SENSEX', 'MIDCPNIFTY']
    symbols = set()

    # Get all unique timestamps
    query = "SELECT DISTINCT timestamp FROM option_data_need"
    df = get_data_from_db(query)

    # Try to extract symbols from timestamps
    if not df.empty and 'timestamp' in df.columns:
        for timestamp in df['timestamp']:
            if timestamp and isinstance(timestamp, str):
                parts = timestamp.split()
                if len(parts) > 2:
                    potential_symbol = parts[2]
                    if potential_symbol in common_symbols:
                        symbols.add(potential_symbol)

    # If no symbols found, try to infer from strike prices
    if not symbols:
        query = "SELECT DISTINCT Strike FROM option_data"
        strikes_df = get_data_from_db(query)

        # Convert strikes to float where possible
        strikes = []
        if not strikes_df.empty and 'Strike' in strikes_df.columns:
            for s in strikes_df['Strike']:
                try:
                    strikes.append(float(s))
                except (ValueError, TypeError):
                    pass

            # Nifty is typically around 15000-25000, BankNifty around 30000-50000
            if any(15000 <= s <= 25000 for s in strikes):
                symbols.add('NIFTY')
            if any(30000 <= s <= 50000 for s in strikes):
                symbols.add('BANKNIFTY')

    # If still no symbols found, try to read from Nifty 500 list
    if not symbols:
        try:
            import csv
            csv_path = 'NSE/ind_nifty500list.csv'
            with open(csv_path, 'r') as f:
                csv_reader = csv.DictReader(f)
                for row in csv_reader:
                    if 'Symbol' in row and row['Symbol']:
                        symbols.add(row['Symbol'])
        except Exception as e:
            print(f"Error reading Nifty 500 list: {e}")

    # If still no symbols found, use default list
    if not symbols:
        symbols = {'NIFTY', 'BANKNIFTY', 'FINNIFTY'}

    return list(symbols)

# API to get available symbols
@app.route('/api/symbols')
@login_required
def get_symbols():
    symbols = extract_symbols_from_db()
    return jsonify(symbols)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')

    app.run(debug=True, host='0.0.0.0', port=5000)
