import os, json
import copy
import pandas as pd
import numpy as np
from kiteconnect import KiteConnect, KiteTicker
import time
import dateutil.parser
import threading
import sys
import requests
import urllib

from py_vollib.black_scholes.implied_volatility import implied_volatility
from py_vollib.black_scholes.greeks.analytical import delta, gamma, rho, theta, vega
import datetime
import configparser
import pymysql
import os
# pd.set_option('future.no_silent_downcasting', True)

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Get MySQL configuration
mysql_config = get_mysql_config()

# Connect to MySQL
conn = pymysql.connect(
    host=mysql_config['host'],
    port=int(mysql_config['port']),
    user=mysql_config['user'],
    password=mysql_config['password'],
    database=mysql_config['database']
)
cursor = conn.cursor()

# Ensure option_data table has the correct structure
def ensure_option_data_table():
    try:
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'option_data'")
        table_exists = cursor.fetchone()

        if not table_exists:
            # Create the table with the important columns
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS option_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp VARCHAR(50),
                symbol VARCHAR(50),
                Strike FLOAT,
                CE_Instrument_Type VARCHAR(10),
                CE_LTP FLOAT,
                CE_LTP_Change FLOAT,
                CE_Open FLOAT,
                CE_High FLOAT,
                CE_Low FLOAT,
                CE_OI INT,
                CE_OI_Change INT,
                PE_Instrument_Type VARCHAR(10),
                PE_LTP FLOAT,
                PE_LTP_Change FLOAT,
                PE_Open FLOAT,
                PE_High FLOAT,
                PE_Low FLOAT,
                PE_OI INT,
                PE_OI_Change INT
            )
            """)
            conn.commit()
            print("Created option_data table with the important columns")
        else:
            # Check if all important columns exist
            cursor.execute("DESCRIBE option_data")
            existing_columns = [column[0] for column in cursor.fetchall()]

            # Define the important columns
            important_columns = [
                'timestamp', 'symbol', 'Strike',
                'CE_Instrument_Type', 'CE_LTP', 'CE_LTP_Change', 'CE_Open', 'CE_High', 'CE_Low', 'CE_OI', 'CE_OI_Change',
                'PE_Instrument_Type', 'PE_LTP', 'PE_LTP_Change', 'PE_Open', 'PE_High', 'PE_Low', 'PE_OI', 'PE_OI_Change'
            ]

            # Add any missing columns
            for column in important_columns:
                if column not in existing_columns:
                    data_type = "VARCHAR(50)" if column in ['timestamp', 'symbol', 'CE_Instrument_Type', 'PE_Instrument_Type'] else \
                                "FLOAT" if column in ['Strike', 'CE_LTP', 'CE_LTP_Change', 'CE_Open', 'CE_High', 'CE_Low',
                                                    'PE_LTP', 'PE_LTP_Change', 'PE_Open', 'PE_High', 'PE_Low'] else \
                                "INT"
                    cursor.execute(f"ALTER TABLE option_data ADD COLUMN {column} {data_type}")
                    print(f"Added column {column} to option_data table")
            conn.commit()

        # Create option_data_live table for live data only
        cursor.execute("SHOW TABLES LIKE 'option_data_live'")
        live_table_exists = cursor.fetchone()

        if not live_table_exists:
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS option_data_live (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp VARCHAR(50),
                symbol VARCHAR(50),
                Strike FLOAT,
                CE_Instrument_Type VARCHAR(10),
                CE_LTP FLOAT,
                CE_LTP_Change FLOAT,
                CE_Open FLOAT,
                CE_High FLOAT,
                CE_Low FLOAT,
                CE_OI INT,
                CE_OI_Change INT,
                PE_Instrument_Type VARCHAR(10),
                PE_LTP FLOAT,
                PE_LTP_Change FLOAT,
                PE_Open FLOAT,
                PE_High FLOAT,
                PE_Low FLOAT,
                PE_OI INT,
                PE_OI_Change INT
            )
            """)
            conn.commit()
            print("Created option_data_live table for live data only")
        else:
            # Check if all important columns exist in live table
            cursor.execute("DESCRIBE option_data_live")
            existing_columns = [column[0] for column in cursor.fetchall()]

            # Add any missing columns to live table
            for column in important_columns:
                if column not in existing_columns:
                    data_type = "VARCHAR(50)" if column in ['timestamp', 'symbol', 'CE_Instrument_Type', 'PE_Instrument_Type'] else \
                                "FLOAT" if column in ['Strike', 'CE_LTP', 'CE_LTP_Change', 'CE_Open', 'CE_High', 'CE_Low',
                                                    'PE_LTP', 'PE_LTP_Change', 'PE_Open', 'PE_High', 'PE_Low'] else \
                                "INT"
                    cursor.execute(f"ALTER TABLE option_data_live ADD COLUMN {column} {data_type}")
                    print(f"Added column {column} to option_data_live table")
            conn.commit()

        # Create option_data_need_live table for live data only
        cursor.execute("SHOW TABLES LIKE 'option_data_need_live'")
        need_live_table_exists = cursor.fetchone()

        if not need_live_table_exists:
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS option_data_need_live (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp TEXT,
                symbol TEXT,
                Spot_LTP REAL,
                FUT_LTP REAL,
                VIX_LTP REAL,
                High REAL,
                Low REAL,
                Spot_LTP_Change REAL,
                FUT_LTP_Change REAL,
                VIX_LTP_Change REAL,

                FUT_OI INTEGER,
                FUT_Change_in_OI INTEGER,

                Total_Call_OI INTEGER,
                Total_Put_OI INTEGER,
                Total_Call_Change_in_OI INTEGER,
                Total_Put_Change_in_OI INTEGER,

                Max_Call_OI INTEGER,
                Max_Put_OI INTEGER,
                Max_Call_OI_Strike REAL,
                Max_Put_OI_Strike REAL,

                Max_Call_Change_in_OI INTEGER,
                Max_Put_Change_in_OI INTEGER,
                Max_Call_Change_in_OI_Strike REAL,
                Max_Put_Change_in_OI_Strike REAL,

                PCR REAL,
                Max_Pain_Strike REAL
            )
            """)
            conn.commit()
            print("Created option_data_need_live table for live data only")

    except Exception as e:
        print(f"Error ensuring tables: {e}")

# Call the function to ensure the tables exist with the correct structure
ensure_option_data_table()

# cursor.execute("""
# CREATE TABLE IF NOT EXISTS option_data (
#     timestamp TEXT,

#     CE_Instrument_Type TEXT,
#     CE_LTP REAL,
#     CE_LTP_Change REAL,
#     CE_LTQ INTEGER,
#     CE_LTT TEXT,
#     CE_Total_Buy_Quantity INTEGER,
#     CE_Total_Sell_Quantity INTEGER,
#     CE_Average_Price REAL,
#     CE_Open REAL,
#     CE_High REAL,
#     CE_Low REAL,
#     CE_Best_Bid_Price REAL,
#     CE_Best_Ask_Price REAL,
#     CE_Prev_Close REAL,
#     CE_Total_Traded_Volume INTEGER,
#     CE_OI INTEGER,
#     CE_OI_Change INTEGER,
#     CE_Intrinsic_Value_Spot REAL,
#     CE_Time_Value_Spot REAL,
#     CE_IV_Spot REAL,
#     CE_Delta_Spot REAL,
#     CE_Gamma_Spot REAL,
#     CE_Rho_Spot REAL,
#     CE_Theta_Spot REAL,
#     CE_Vega_Spot REAL,

#     Strike REAL,

#     PE_Instrument_Type TEXT,
#     PE_LTP REAL,
#     PE_LTP_Change REAL,
#     PE_LTQ INTEGER,
#     PE_LTT TEXT,
#     PE_Total_Buy_Quantity INTEGER,
#     PE_Total_Sell_Quantity INTEGER,
#     PE_Average_Price REAL,
#     PE_Open REAL,
#     PE_High REAL,
#     PE_Low REAL,
#     PE_Best_Bid_Price REAL,
#     PE_Best_Ask_Price REAL,
#     PE_Prev_Close REAL,
#     PE_Total_Traded_Volume INTEGER,
#     PE_OI INTEGER,
#     PE_OI_Change INTEGER,
#     PE_Intrinsic_Value_Spot REAL,
#     PE_Time_Value_Spot REAL,
#     PE_IV_Spot REAL,
#     PE_Delta_Spot REAL,
#     PE_Gamma_Spot REAL,
#     PE_Rho_Spot REAL,
#     PE_Theta_Spot REAL,
#     PE_Vega_Spot REAL
# )
# """)
# conn.commit()


# Tables are created in create_mysql_tables.py
cursor.execute("""
CREATE TABLE IF NOT EXISTS option_data_need (
    timestamp TEXT,
    symbol TEXT,
    Spot_LTP REAL,
    FUT_LTP REAL,
    VIX_LTP REAL,
    High REAL,
    Low REAL,
    Spot_LTP_Change REAL,
    FUT_LTP_Change REAL,
    VIX_LTP_Change REAL,

    FUT_OI INTEGER,
    FUT_Change_in_OI INTEGER,

    Total_Call_OI INTEGER,
    Total_Put_OI INTEGER,
    Total_Call_Change_in_OI INTEGER,
    Total_Put_Change_in_OI INTEGER,

    Max_Call_OI INTEGER,
    Max_Put_OI INTEGER,
    Max_Call_OI_Strike REAL,
    Max_Put_OI_Strike REAL,

    Max_Call_Change_in_OI INTEGER,
    Max_Put_Change_in_OI INTEGER,
    Max_Call_Change_in_OI_Strike REAL,
    Max_Put_Change_in_OI_Strike REAL,

    PCR REAL,
    Max_Pain_Strike REAL
)
""")
conn.commit()





def get_login_credentials():
    global login_credential

    def login_credentials():
        print("---- Enter you Zerodha Login Credentials  ----")
        login_credential = {"api_key": str(input("Enter API Key : ").strip()),
                            "api_secret": str(input("Enter API Secret : ")).strip()}
        if input("Press Y to save login credential and any key to bypass : ").strip().upper() == "Y":
            with open(f"zerodha_login_details.json", "w") as f:
                json.dump(login_credential, f)
            print("Data Saved...")
        else:
            print("Data Save canceled!!!!!")

    while True:
        try:
            with open(f"zerodha_login_details.json", "r") as f:
                login_credential = json.load(f)
            break
        except:
            login_credentials()
    return login_credential


def get_access_token():
    global login_credential, access_token

    def login():
        global login_credential, access_token
        print("Trying Log In...")
        if login_credential["api_key"] == "TradeViaPython":
            print("Login url : ", "https://kite.zerodha.com ( Don't Login Anywhere else after this, Instead of mobile App. )")
            access_token = input("Login and enter your 'enctoken' here : ")
        else:
            kite = KiteConnect(api_key=login_credential["api_key"])
            print("Login url : ", kite.login_url())
            request_tkn = input("Login and enter your 'request_token' here : ")
            try:
                access_token = kite.generate_session(request_token=request_tkn, api_secret=login_credential["api_secret"])['access_token']
            except Exception as e:
                print(f"Login Failed {e}!!!!!")
        os.makedirs(f"AccessToken", exist_ok=True)
        with open(f"AccessToken/{datetime.datetime.now().date()}.json", "w") as f:
            json.dump(access_token, f)

    while True:
        if os.path.exists(f"AccessToken/{datetime.datetime.now().date()}.json"):
            with open(f"AccessToken/{datetime.datetime.now().date()}.json", "r") as f:
                access_token = json.load(f)
            break
        else:
            login()
    return access_token


def get_object():
    global kite, login_credential, access_token, user_id
    try:
        if login_credential["api_key"] == "TradeViaPython":
            class KiteApp:
                # Products
                PRODUCT_MIS = "MIS"
                PRODUCT_CNC = "CNC"
                PRODUCT_NRML = "NRML"
                PRODUCT_CO = "CO"

                # Order types
                ORDER_TYPE_MARKET = "MARKET"
                ORDER_TYPE_LIMIT = "LIMIT"
                ORDER_TYPE_SLM = "SL-M"
                ORDER_TYPE_SL = "SL"

                # Varities
                VARIETY_REGULAR = "regular"
                VARIETY_CO = "co"
                VARIETY_AMO = "amo"

                # Transaction type
                TRANSACTION_TYPE_BUY = "BUY"
                TRANSACTION_TYPE_SELL = "SELL"

                # Validity
                VALIDITY_DAY = "DAY"
                VALIDITY_IOC = "IOC"

                # Exchanges
                EXCHANGE_NSE = "NSE"
                EXCHANGE_BSE = "BSE"
                EXCHANGE_NFO = "NFO"
                EXCHANGE_CDS = "CDS"
                EXCHANGE_BFO = "BFO"
                EXCHANGE_MCX = "MCX"

                def __init__(self, enctoken):
                    self.enctoken = enctoken
                    self.headers = {"Authorization": f"enctoken {self.enctoken}"}
                    self.session = requests.session()
                    self.root_url = "https://kite.zerodha.com/oms"
                    self.session.get(self.root_url, headers=self.headers)

                def instruments(self, exchange=None):
                    data = self.session.get(f"https://api.kite.trade/instruments").text.split("\n")
                    Exchange = []
                    for i in data[1:-1]:
                        row = i.split(",")
                        if exchange is None or exchange == row[11]:
                            Exchange.append(
                                {'instrument_token': int(row[0]), 'exchange_token': row[1], 'tradingsymbol': row[2],
                                 'name': row[3][1:-1], 'last_price': float(row[4]),
                                 'expiry': dateutil.parser.parse(row[5]).date() if row[5] != "" else None,
                                 'strike': float(row[6]), 'tick_size': float(row[7]), 'lot_size': int(row[8]),
                                 'instrument_type': row[9], 'segment': row[10],
                                 'exchange': row[11]})
                    return Exchange

                def historical_data(self, instrument_token, from_date, to_date, interval, continuous=False, oi=False):
                    params = {"from": from_date,
                              "to": to_date,
                              "interval": interval,
                              "continuous": 1 if continuous else 0,
                              "oi": 1 if oi else 0}
                    lst = self.session.get(
                        f"{self.root_url}/instruments/historical/{instrument_token}/{interval}", params=params,
                        headers=self.headers).json()["data"]["candles"]
                    records = []
                    for i in lst:
                        record = {"date": dateutil.parser.parse(i[0]), "open": i[1], "high": i[2], "low": i[3],
                                  "close": i[4], "volume": i[5], }
                        if len(i) == 7:
                            record["oi"] = i[6]
                        records.append(record)
                    return records

                def margins(self):
                    margins = self.session.get(f"{self.root_url}/user/margins", headers=self.headers).json()["data"]
                    return margins

                def profile(self):
                    profile = self.session.get(f"{self.root_url}/user/profile", headers=self.headers).json()["data"]
                    return profile

                def orders(self):
                    orders = self.session.get(f"{self.root_url}/orders", headers=self.headers).json()["data"]
                    return orders

                def positions(self):
                    positions = self.session.get(f"{self.root_url}/portfolio/positions", headers=self.headers).json()[
                        "data"]
                    return positions

                def place_order(self, variety, exchange, tradingsymbol, transaction_type, quantity, product, order_type,
                                price=None,
                                validity=None, disclosed_quantity=None, trigger_price=None, squareoff=None,
                                stoploss=None,
                                trailing_stoploss=None, tag=None):
                    params = locals()
                    del params["self"]
                    for k in list(params.keys()):
                        if params[k] is None:
                            del params[k]
                    order_id = self.session.post(f"{self.root_url}/orders/{variety}",
                                                 data=params, headers=self.headers).json()["data"]["order_id"]
                    return order_id

                def modify_order(self, variety, order_id, parent_order_id=None, quantity=None, price=None,
                                 order_type=None,
                                 trigger_price=None, validity=None, disclosed_quantity=None):
                    params = locals()
                    del params["self"]
                    for k in list(params.keys()):
                        if params[k] is None:
                            del params[k]

                    order_id = self.session.put(f"{self.root_url}/orders/{variety}/{order_id}",
                                                data=params, headers=self.headers).json()["data"][
                        "order_id"]
                    return order_id

                def cancel_order(self, variety, order_id, parent_order_id=None):
                    order_id = self.session.delete(f"{self.root_url}/orders/{variety}/{order_id}",
                                                   data={"parent_order_id": parent_order_id} if parent_order_id else {},
                                                   headers=self.headers).json()["data"]["order_id"]
                    return order_id

            kite = KiteApp(enctoken=access_token)
        else:
            kite = KiteConnect(api_key=login_credential["api_key"], access_token=access_token)

        user_id = kite.profile()["user_id"]
        user_name = kite.profile()["user_name"]
        print(f"Logged In : {user_id} as {user_name}")
    except Exception as e:
        print(f"Login Error {e}!!!!!")
        os.remove(f"AccessToken/{datetime.datetime.now().date()}.json") if os.path.exists(
            f"AccessToken/{datetime.datetime.now().date()}.json") else None
        time.sleep(5)
        sys.exit()


def start_websocket():
    global login_credential, access_token, user_id, kws, tick_data, symbol_token, token_symbol
    access_token = access_token+"&user_id="+user_id if login_credential["api_key"] == "TradeViaPython" else access_token
    kws = KiteTicker(api_key=login_credential["api_key"], access_token=access_token)

    tick_data = {}
    token_symbol = {}


    def on_ticks(ws, ticks):
        for i in ticks:
            tick_data[token_symbol[i["instrument_token"]]] = i


    kws.on_ticks = on_ticks
    kws.connect(threaded=True)
    while not kws.is_connected():
        time.sleep(1)
    print("WebSocket : Connected")

def send_telegram_message(message):
    encoded_message = urllib.parse.quote(message)
    base_url = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001970206797&text={encoded_message}"
    base_url1 = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001878975708&text={encoded_message}"
    response = requests.get(base_url)
    response = requests.get(base_url1)

get_login_credentials()
get_access_token()
get_object()
start_websocket()

print("----Option Chain----")
print("Running in terminal mode - no Excel file will be used")

# Function to get user input for symbol, expiry dates, and calculation base
def get_user_inputs():
    global exchange, inp_symbol, inp_oc_expiry, inp_fut_expiry, inp_calc_base_fut

    # Download exchange data if not already done
    exchange = None
    while True:
        if exchange is None:
            try:
                print("Downloading exchange data...")
                exchange = pd.DataFrame(kite.instruments())
                break
            except:
                print("Exchange Download Error...")
                time.sleep(10)

    # Get FNO symbols
    df = copy.deepcopy(exchange)
    df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
    fno_symbols = list(df["name"].unique())
    fno_symbols = [sym for sym in fno_symbols if sym != "SENSEX50"]

    # Display available symbols
    print("\nAvailable FNO Symbols:")
    # for i, symbol in enumerate(fno_symbols):
    #     print(f"{i+1}. {symbol}")

    # Get symbol input
    while True:
        try:
            symbol_choice = '4'
            if symbol_choice.isdigit() and 1 <= int(symbol_choice) <= len(fno_symbols):
                inp_symbol = fno_symbols[int(symbol_choice) - 1]
            elif symbol_choice.upper() in fno_symbols:
                inp_symbol = symbol_choice.upper()
            else:
                print("Invalid symbol. Please try again.")
                continue
            break
        except Exception as e:
            print(f"Error: {e}. Please try again.")

    print(f"\nSelected Symbol: {inp_symbol}")

    # Get option expiry dates
    df = copy.deepcopy(exchange)
    df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
    df = df[df["name"] == inp_symbol]
    df = df[(df["instrument_type"] == "CE") | (df["instrument_type"] == "PE")]
    oc_expiries_list = sorted(list(df["expiry"].unique()))

    print("\nAvailable Option Expiry Dates:")
    # for i, expiry in enumerate(oc_expiries_list):
    #     print(f"{i+1}. {expiry.strftime('%Y-%m-%d')}")

    # Get option expiry input
    while True:
        try:
            expiry_choice = '1'
            if expiry_choice.isdigit() and 1 <= int(expiry_choice) <= len(oc_expiries_list):
                inp_oc_expiry = oc_expiries_list[int(expiry_choice) - 1]
                break
            else:
                print("Invalid choice. Please try again.")
        except Exception as e:
            print(f"Error: {e}. Please try again.")

    print(f"Selected Option Expiry: {inp_oc_expiry.strftime('%Y-%m-%d')}")

    # Get futures expiry dates
    df = copy.deepcopy(exchange)
    df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
    df = df[df["name"] == inp_symbol]
    df = df[df["instrument_type"] == "FUT"]
    fut_expiries_list = sorted(list(df["expiry"].unique()))

    print("\nAvailable Futures Expiry Dates:")
    # for i, expiry in enumerate(fut_expiries_list):
    #     print(f"{i+1}. {expiry.strftime('%Y-%m-%d')}")

    # Get futures expiry input
    while True:
        try:
            expiry_choice = '1'
            if expiry_choice.isdigit() and 1 <= int(expiry_choice) <= len(fut_expiries_list):
                inp_fut_expiry = fut_expiries_list[int(expiry_choice) - 1]
                break
            else:
                print("Invalid choice. Please try again.")
        except Exception as e:
            print(f"Error: {e}. Please try again.")

    print(f"Selected Futures Expiry: {inp_fut_expiry.strftime('%Y-%m-%d')}")

    # Get calculation base
    while True:
        try:
            calc_base = 'y'
            if calc_base in ['y', 'yes']:
                inp_calc_base_fut = True
                break
            elif calc_base in ['n', 'no']:
                inp_calc_base_fut = False
                break
            else:
                print("Invalid choice. Please enter 'y' or 'n'.")
        except Exception as e:
            print(f"Error: {e}. Please try again.")

    print(f"Calculation Base: {'Futures' if inp_calc_base_fut else 'Spot'}")

    return inp_symbol, inp_oc_expiry, inp_fut_expiry, inp_calc_base_fut

# Function to display data in terminal
def display_data(data_dict, title="Data"):
    print(f"\n{'-'*20} {title} {'-'*20}")
    for key, value in data_dict.items():
        if value is not None:
            print(f"{key}: {value}")

# Download exchange data
exchange = None
while True:
    if exchange is None:
        try:
            print("Downloading exchange data...")
            exchange = pd.DataFrame(kite.instruments())
            break
        except:
            print("Exchange Download Error...")
            time.sleep(10)
# Initialize variables
pre_symbol = pre_oc_expiry = pre_fut_expiry = ""
oc_expiries_list = []
fut_expiries_list = []
instrument_dict = {}
prev_day_oi = {}
stop_thread = False


def get_oi(data):
    global prev_day_oi, kite, stop_thread
    for symbol, v in data.items():
        if stop_thread:
            break
        while True:
            try:
                prev_day_oi[symbol]
                break
            except:
                try:
                    pre_day_data = kite.historical_data(v["token"], (datetime.datetime.now() - datetime.timedelta(days=30)).date(),
                                          (datetime.datetime.now() - datetime.timedelta(days=1)).date(), "day", oi=True)
                    try:
                        prev_day_oi[symbol] = pre_day_data[-1]["oi"]
                    except:
                        prev_day_oi[symbol] = 0
                    break
                except Exception as e:
                    time.sleep(0.5)

# Define variables to store previous values
prev_call_oi_value = None
prev_call_oi_change = None
prev_put_oi_value = None
prev_put_oi_change = None

# Define market hours
MARKET_START_TIME = datetime.time(9, 15)  # Market opens at 9:15 AM
MARKET_END_TIME = datetime.time(15, 30)  # Market closes at 3:30 PM

# Function to check if market is open
def is_market_open():
    now = datetime.datetime.now().time()
    return MARKET_START_TIME <= now <= MARKET_END_TIME

print("Welcome to Option Tech Analysis")

# Get user inputs
inp_symbol, inp_oc_expiry, inp_fut_expiry, inp_calc_base_fut = get_user_inputs()

# Setup for data collection
if token_symbol:
    kws.unsubscribe(list(token_symbol.keys()))
    time.sleep(2)
    tick_data = {}
    token_symbol = {}
instrument_dict = {}
stop_thread = True
time.sleep(2)
oc_expiries_list = []
fut_expiries_list = []
pre_symbol = inp_symbol
pre_oc_expiry = inp_oc_expiry
pre_fut_expiry = inp_fut_expiry

# Main loop
while True:
    if inp_symbol is not None:
        # Check if market is open
        if not is_market_open():
            current_time = datetime.datetime.now().strftime("%H:%M:%S")
            print(f"[{current_time}] Market is closed. Waiting for market hours (9:15 AM - 3:30 PM)...")
            time.sleep(1)  # Check every minute when market is closed
            continue

        # Get the current time to ensure we run exactly once per second
        start_time = time.time()
        try:
            # Option expiry dates already loaded in get_user_inputs()
            if not instrument_dict and inp_oc_expiry is not None and inp_fut_expiry is not None:
                df = copy.deepcopy(exchange)
                df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
                df = df[df["name"] == inp_symbol]
                df = df[(df["instrument_type"] == "CE") | (df["instrument_type"] == "PE")]
                # Check if inp_oc_expiry is already a date object or a datetime object
                if isinstance(inp_oc_expiry, datetime.datetime):
                    df = df[df["expiry"] == inp_oc_expiry.date()]
                else:  # Already a date object
                    df = df[df["expiry"] == inp_oc_expiry]
                lot_size = list(df["lot_size"])[0]
                for i in df.index:
                    instrument_dict[f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'] = {"strikePrice": float(df["strike"][i]),
                                                                        "instrumentType": df["instrument_type"][i],
                                                                        "token": df["instrument_token"][i]}
                    token_symbol[int(df["instrument_token"][i])] = f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'
                df = copy.deepcopy(exchange)
                df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
                df = df[df["name"] == inp_symbol]
                df = df[df["instrument_type"] == "FUT"]
                # Check if inp_fut_expiry is already a date object or a datetime object
                if isinstance(inp_fut_expiry, datetime.datetime):
                    df = df[df["expiry"] == inp_fut_expiry.date()]
                else:  # Already a date object
                    df = df[df["expiry"] == inp_fut_expiry]
                for i in df.index:
                    fut_instrument = f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'
                    instrument_dict[f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'] = {"strikePrice": float(df["strike"][i]),
                                                                        "instrumentType": df["instrument_type"][i],
                                                                        "token": df["instrument_token"][i]}

                    token_symbol[int(df["instrument_token"][i])] = f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'
                stop_thread = False
                thread = threading.Thread(target=get_oi, args=(instrument_dict,))
                thread.start()
            option_data = {}
            fut_data = {}
            spot_data = {}
            vix_data = {}
            index_map = {"NIFTY": "NSE:NIFTY 50", "BANKNIFTY": "NSE:NIFTY BANK", "FINNIFTY": "NSE:NIFTY FIN SERVICE",
                         "MIDCPNIFTY": "NSE:NIFTY MID SELECT", "SENSEX": "BSE:SENSEX", "BANKEX": "BSE:BANKEX",
                         "SENSEX50": "BSE:SENSEX50"}
            spot_instrument = index_map[inp_symbol] if inp_symbol in list(index_map) else f"NSE:{inp_symbol}"
            if not spot_instrument in list(token_symbol.values()):
                spot_df = copy.deepcopy(exchange)
                spot_token = list(spot_df[((spot_df["exchange"] == spot_instrument[:3]) & (spot_df["tradingsymbol"] == spot_instrument[4:]))]["instrument_token"])[0]
                token_symbol[int(spot_token)] = spot_instrument
                vix_df = copy.deepcopy(exchange)
                vix_token = list(
                    vix_df[((vix_df["exchange"] == "NSE") & (vix_df["tradingsymbol"] == "INDIA VIX"))][
                        "instrument_token"])[0]
                token_symbol[int(vix_token)] = "NSE:INDIA VIX"
                kws.subscribe(list(token_symbol.keys()))
                kws.set_mode(kws.MODE_FULL, list(token_symbol.keys()))
                time.sleep(2)

            for symbol, values in tick_data.copy().items():
                if symbol == spot_instrument:
                    spot_data = values
                elif symbol == "NSE:INDIA VIX":
                    vix_data = values
                elif symbol == fut_instrument:
                    fut_data = values

            for symbol, values in tick_data.copy().items():
                if symbol == spot_instrument or symbol == "NSE:INDIA VIX" or symbol == fut_instrument:
                    pass
                else:
                    try:
                        try:
                            option_data[symbol]
                        except:
                            option_data[symbol] = {}
                        option_data[symbol]["Strike_Price"] = instrument_dict[symbol]["strikePrice"]
                        option_data[symbol]["Instrument_Type"] = instrument_dict[symbol]["instrumentType"]
                        option_data[symbol]["LTP"] = values["last_price"]
                        option_data[symbol]["LTP_Change"] = values["last_price"] - values["ohlc"]["close"] if values["last_price"] != 0 else 0
                        option_data[symbol]["LTQ"] = values["last_traded_quantity"]
                        option_data[symbol]["LTT"] = values["last_trade_time"]
                        option_data[symbol]["Total_Buy_Quantity"] = values["total_buy_quantity"]
                        option_data[symbol]["Total_Sell_Quantity"] = values["total_sell_quantity"]
                        option_data[symbol]["Average_Price"] = values["average_traded_price"]
                        option_data[symbol]["Open"] = values["ohlc"]["open"]
                        option_data[symbol]["High"] = values["ohlc"]["high"]
                        option_data[symbol]["Low"] = values["ohlc"]["low"]
                        option_data[symbol]["Best_Bid_Price"] = values["depth"]["buy"][0]["price"]
                        option_data[symbol]["Best_Ask_Price"] = values["depth"]["sell"][0]["price"]
                        option_data[symbol]["Prev_Close"] = values["ohlc"]["close"]
                        option_data[symbol]["Total_Traded_Volume"] = values["volume_traded"]
                        option_data[symbol]["OI"] = int(values["oi"]/lot_size)
                        try:
                            option_data[symbol]["OI_Change"] = int((values["oi"] - prev_day_oi[symbol])/lot_size)
                        except:
                            option_data[symbol]["OI_Change"] = None
                        if instrument_dict[symbol]["instrumentType"] == "CE":
                            option_data[symbol]["Intrinsic_Value" + ("(Fut)" if inp_calc_base_fut is True else "(Spot)")] = (fut_data["last_price"] if inp_calc_base_fut is True else spot_data["last_price"]) - instrument_dict[symbol]["strikePrice"]
                            option_data[symbol]["Time_Value" + ("(Fut)" if inp_calc_base_fut is True else "(Spot)")] = values["last_price"] - ((fut_data["last_price"] if inp_calc_base_fut is True else spot_data["last_price"]) - instrument_dict[symbol]["strikePrice"])
                        else:
                            option_data[symbol]["Intrinsic_Value" + ("(Fut)" if inp_calc_base_fut is True else "(Spot)")] = instrument_dict[symbol]["strikePrice"] - (fut_data["last_price"] if inp_calc_base_fut is True else spot_data["last_price"])
                            option_data[symbol]["Time_Value" + ("(Fut)" if inp_calc_base_fut is True else "(Spot)")] = values["last_price"] - (instrument_dict[symbol]["strikePrice"] - (fut_data["last_price"] if inp_calc_base_fut is True else spot_data["last_price"]))

                        def greeks(premium, expiry, asset_price, strike_price, intrest_rate, instrument_type):
                            try:
                                t = ((datetime.datetime(expiry.year, expiry.month, expiry.day, 15,
                                                        30) - datetime.datetime.now()) / datetime.timedelta(
                                    days=1)) / 365
                                S = asset_price
                                K = strike_price
                                r = intrest_rate
                                if premium == 0 or t <= 0 or S <= 0 or K <= 0 or r <= 0:
                                    raise Exception
                                flag = instrument_type[0].lower()
                                imp_v = implied_volatility(premium, S, K, t, r, flag)
                                return {"IV": imp_v,
                                        "Delta": delta(flag, S, K, t, r, imp_v),
                                        "Gamma": gamma(flag, S, K, t, r, imp_v),
                                        "Rho": rho(flag, S, K, t, r, imp_v),
                                        "Theta": theta(flag, S, K, t, r, imp_v),
                                        "Vega": vega(flag, S, K, t, r, imp_v)}
                            except:
                                return {"IV": 0,
                                        "Delta": 0,
                                        "Gamma": 0,
                                        "Rho": 0,
                                        "Theta": 0,
                                        "Vega": 0}

                        # Check if inp_oc_expiry is already a date object or a datetime object
                        expiry_date = inp_oc_expiry if isinstance(inp_oc_expiry, datetime.date) else inp_oc_expiry.date()
                        greek = greeks(values["last_price"],
                                       expiry_date,
                                       (fut_data["last_price"] if inp_calc_base_fut is True else spot_data["last_price"]),
                                       instrument_dict[symbol]["strikePrice"],
                                       0.1,
                                       instrument_dict[symbol]["instrumentType"])
                        for k, v in greek.items():
                            option_data[symbol][k + ("(Fut)" if inp_calc_base_fut is True else "(Spot)")] = v
                    except Exception as e:
                        print(e)
                        pass

            df = pd.DataFrame(option_data).transpose()
            ce_df = df[df["Instrument_Type"] == "CE"]
            ce_df = ce_df.rename(columns={i: f"CE_{i}" for i in list(ce_df.keys())})
            ce_df.index = ce_df["CE_Strike_Price"]
            ce_df = ce_df.drop(["CE_Strike_Price"], axis=1)
            ce_df["Strike"] = ce_df.index

            pe_df = df[df["Instrument_Type"] == "PE"]
            pe_df = pe_df.rename(columns={i: f"PE_{i}" for i in list(pe_df.keys())})
            pe_df.index = pe_df["PE_Strike_Price"]
            pe_df = pe_df.drop("PE_Strike_Price", axis=1)
            df = pd.concat([ce_df, pe_df], axis=1).sort_index()
            df = df.replace(np.nan, 0)
            df["Strike"] = df.index
            total_profit_loss = {}
            for i in df.index:
                itm_call = df[df.index < i]
                itm_call_loss = (i - itm_call.index) * itm_call["CE_OI"]
                itm_put = df[df.index > i]
                itm_put_loss = (itm_put.index - i) * itm_put["PE_OI"]
                total_profit_loss[sum(itm_call_loss) + sum(itm_put_loss)] = i
            df.index = [np.nan] * len(df)
            try:
                fut_change_oi = fut_data["oi"] - prev_day_oi[fut_instrument]
            except:
                fut_change_oi = 0
            # Create a dictionary for display
            market_data = {
                "Spot LTP": spot_data["last_price"],
                "FUT LTP": fut_data["last_price"],
                "VIX LTP": vix_data["last_price"],
                "High": spot_data["ohlc"]["high"],
                "Low": spot_data["ohlc"]["low"],
                "Spot LTP Change": spot_data["last_price"] - spot_data["ohlc"]["close"],
                "FUT LTP Change": fut_data["last_price"] - fut_data["ohlc"]["close"],
                "VIX LTP Change": vix_data["last_price"] - vix_data["ohlc"]["close"],
                "FUT OI": fut_data["oi"],
                "FUT Change in OI": fut_change_oi,
                "Total Call OI": sum(list(df["CE_OI"])),
                "Total Put OI": sum(list(df["PE_OI"])),
                "Total Call Change in OI": sum(list(df["CE_OI_Change"])),
                "Total Put Change in OI": sum(list(df["PE_OI_Change"])),
                "Max Call OI": max(list(df["CE_OI"])),
                "Max Put OI": max(list(df["PE_OI"])),
                "Max Call OI Strike": list(df[df["CE_OI"] == max(list(df["CE_OI"]))]["Strike"])[0],
                "Max Put OI Strike": list(df[df["PE_OI"] == max(list(df["PE_OI"]))]["Strike"])[0],
                "Max Call Change in OI": max(list(df["CE_OI_Change"])),
                "Max Put Change in OI": max(list(df["PE_OI_Change"])),
                "Max Call Change in OI Strike": list(df[df["CE_OI_Change"] == max(list(df["CE_OI_Change"]))]["Strike"])[0],
                "Max Put Change in OI Strike": list(df[df["PE_OI_Change"] == max(list(df["PE_OI_Change"]))]["Strike"])[0],
                "PCR": round((sum(list(df["PE_OI"]))/sum(list(df["CE_OI"])) if sum(list(df["CE_OI"])) != 0 else 0), 2),
                "Max Pain Strike": total_profit_loss[min(list(total_profit_loss.keys()))]
            }

            # Display data in terminal
            display_data(market_data, "Market Data")

            # Store values for telegram message
            nifty_value = spot_data["last_price"]
            today_high = spot_data["ohlc"]["high"]
            today_low = spot_data["ohlc"]["low"]
            pcr_value = market_data["PCR"]
            current_call_oi_value = market_data["Max Call OI"]
            current_call_oi_change = market_data["Max Call Change in OI"]
            current_put_oi_value = market_data["Max Put OI"]
            current_put_oi_change = market_data["Max Put Change in OI"]
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # Data preparation
            record = (
                timestamp,
                spot_data["last_price"],
                fut_data["last_price"],
                vix_data["last_price"],
                spot_data["ohlc"]["high"],
                spot_data["ohlc"]["low"],
                spot_data["last_price"] - spot_data["ohlc"]["close"],
                fut_data["last_price"] - fut_data["ohlc"]["close"],
                vix_data["last_price"] - vix_data["ohlc"]["close"],

                fut_data["oi"],
                fut_change_oi,

                sum(df["CE_OI"]),
                sum(df["PE_OI"]),
                sum(df["CE_OI_Change"]),
                sum(df["PE_OI_Change"]),

                max(df["CE_OI"]),
                max(df["PE_OI"]),
                df[df["CE_OI"] == max(df["CE_OI"])]["Strike"].iloc[0],
                df[df["PE_OI"] == max(df["PE_OI"])]["Strike"].iloc[0],

                max(df["CE_OI_Change"]),
                max(df["PE_OI_Change"]),
                df[df["CE_OI_Change"] == max(df["CE_OI_Change"])]["Strike"].iloc[0],
                df[df["PE_OI_Change"] == max(df["PE_OI_Change"])]["Strike"].iloc[0],

                round((sum(df["PE_OI"]) / sum(df["CE_OI"])) if sum(df["CE_OI"]) != 0 else 0, 2),
                total_profit_loss[min(total_profit_loss.keys())]
            )

            # Insert into SQLite
            # Add symbol to the record
            record_with_symbol = (record[0], inp_symbol) + record[1:]

            # Insert into option_data_need (historical data)
            cursor.execute("""
                INSERT INTO option_data_need (
                    timestamp, symbol, Spot_LTP, FUT_LTP, VIX_LTP, High, Low,
                    Spot_LTP_Change, FUT_LTP_Change, VIX_LTP_Change,
                    FUT_OI, FUT_Change_in_OI,
                    Total_Call_OI, Total_Put_OI, Total_Call_Change_in_OI, Total_Put_Change_in_OI,
                    Max_Call_OI, Max_Put_OI, Max_Call_OI_Strike, Max_Put_OI_Strike,
                    Max_Call_Change_in_OI, Max_Put_Change_in_OI, Max_Call_Change_in_OI_Strike, Max_Put_Change_in_OI_Strike,
                    PCR, Max_Pain_Strike
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """, record_with_symbol)

            # Check if there's already a record for this symbol in option_data_need_live
            cursor.execute("SELECT id FROM option_data_need_live WHERE symbol = %s", (inp_symbol,))
            existing_record = cursor.fetchone()

            if existing_record:
                # Update existing record
                cursor.execute("""
                    UPDATE option_data_need_live SET
                        timestamp = %s, Spot_LTP = %s, FUT_LTP = %s, VIX_LTP = %s, High = %s, Low = %s,
                        Spot_LTP_Change = %s, FUT_LTP_Change = %s, VIX_LTP_Change = %s,
                        FUT_OI = %s, FUT_Change_in_OI = %s,
                        Total_Call_OI = %s, Total_Put_OI = %s, Total_Call_Change_in_OI = %s, Total_Put_Change_in_OI = %s,
                        Max_Call_OI = %s, Max_Put_OI = %s, Max_Call_OI_Strike = %s, Max_Put_OI_Strike = %s,
                        Max_Call_Change_in_OI = %s, Max_Put_Change_in_OI = %s, Max_Call_Change_in_OI_Strike = %s, Max_Put_Change_in_OI_Strike = %s,
                        PCR = %s, Max_Pain_Strike = %s
                    WHERE symbol = %s
                """, (record[0],) + record[1:] + (inp_symbol,))
                print(f"Updated live data for symbol {inp_symbol} in option_data_need_live")
            else:
                # Insert new record
                cursor.execute("""
                    INSERT INTO option_data_need_live (
                        timestamp, symbol, Spot_LTP, FUT_LTP, VIX_LTP, High, Low,
                        Spot_LTP_Change, FUT_LTP_Change, VIX_LTP_Change,
                        FUT_OI, FUT_Change_in_OI,
                        Total_Call_OI, Total_Put_OI, Total_Call_Change_in_OI, Total_Put_Change_in_OI,
                        Max_Call_OI, Max_Put_OI, Max_Call_OI_Strike, Max_Put_OI_Strike,
                        Max_Call_Change_in_OI, Max_Put_Change_in_OI, Max_Call_Change_in_OI_Strike, Max_Put_Change_in_OI_Strike,
                        PCR, Max_Pain_Strike
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, record_with_symbol)
                print(f"Inserted new live data for symbol {inp_symbol} in option_data_need_live")

            conn.commit()
            current_time = datetime.datetime.now().time()
            formatted_time = current_time.strftime("%H:%M:%S")
            # Add timestamp and symbol to the dataframe
            df["timestamp"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            df["symbol"] = inp_symbol
            formatted_expiry = inp_oc_expiry.strftime("%Y-%m-%d")

            # Print option chain summary
            print(f"\n{'-'*20} Option Chain Summary {'-'*20}")
            print(f"Time: {formatted_time}")
            print(f"Symbol: {inp_symbol}")
            # print(f"Expiry: {formatted_expiry}")
            # df.to_sql("option_data", conn, if_exists="append", index=False)
            # Define the important columns to store
            important_columns = [
                'timestamp',
                'symbol',
                'Strike',
                # CE side
                'CE_Instrument_Type',
                'CE_LTP',
                'CE_LTP_Change',
                'CE_Open',
                'CE_High',
                'CE_Low',
                'CE_OI',
                'CE_OI_Change',
                # PE side
                'PE_Instrument_Type',
                'PE_LTP',
                'PE_LTP_Change',
                'PE_Open',
                'PE_High',
                'PE_Low',
                'PE_OI',
                'PE_OI_Change'
            ]

            # Find ATM strike (closest to spot price)
            spot_price = spot_data["last_price"]

            # Manual calculation to find ATM strike
            min_diff = float('inf')
            atm_strike = None

            for strike in df['Strike'].unique():
                try:
                    # Convert strike to float if it's not already
                    strike_val = float(strike)
                    diff = abs(strike_val - spot_price)

                    if diff < min_diff:
                        min_diff = diff
                        atm_strike = strike
                except (ValueError, TypeError):
                    # Skip if strike can't be converted to float
                    continue

            # If we couldn't find an ATM strike, use the middle strike
            if atm_strike is None:
                all_strikes = sorted(df['Strike'].unique())
                if all_strikes:
                    atm_strike = all_strikes[len(all_strikes) // 2]

            # Get 5 ITM and 5 OTM strikes around the ATM strike
            all_strikes = sorted(df['Strike'].unique())
            atm_index = all_strikes.index(atm_strike)

            # Calculate start and end indices for the 11 strikes (1 ATM, 5 ITM, 5 OTM)
            start_index = max(0, atm_index - 5)  # 5 ITM strikes
            end_index = min(len(all_strikes) - 1, atm_index + 5)  # 5 OTM strikes

            # Adjust if we don't have enough strikes on either side
            if start_index > 0 and end_index < len(all_strikes) - 1:
                # We have enough strikes on both sides
                selected_strikes = all_strikes[start_index:end_index + 1]
            elif start_index == 0:
                # Not enough ITM strikes, take more OTM strikes
                selected_strikes = all_strikes[0:min(11, len(all_strikes))]
            else:
                # Not enough OTM strikes, take more ITM strikes
                selected_strikes = all_strikes[max(0, len(all_strikes) - 11):len(all_strikes)]

            # Filter dataframe to only include the selected strikes
            filtered_df = df[df['Strike'].isin(selected_strikes)]

            # Filter columns to only include important ones
            available_columns = [col for col in important_columns if col in filtered_df.columns]
            filtered_df = filtered_df[available_columns]

            try:
                # Convert filtered dataframe to list of tuples
                values = filtered_df.to_dict('records')

                # Prepare the SQL query
                columns = ', '.join([f'`{col}`' for col in available_columns])
                placeholders = ', '.join(['%s'] * len(available_columns))

                # Insert data into option_data (historical)
                for value in values:
                    try:
                        # Get values for the important columns
                        row_values = [value[col] for col in available_columns]

                        cursor.execute(f"""
                            INSERT INTO option_data ({columns})
                            VALUES ({placeholders})
                        """, tuple(row_values))
                    except Exception as e:
                        print(f"Error inserting row into option_data: {e}")

                # First, delete any existing records for this symbol in option_data_live
                cursor.execute("DELETE FROM option_data_live WHERE symbol = %s", (inp_symbol,))

                # Then insert the new data into option_data_live
                for value in values:
                    try:
                        # Get values for the important columns
                        row_values = [value[col] for col in available_columns]

                        cursor.execute(f"""
                            INSERT INTO option_data_live ({columns})
                            VALUES ({placeholders})
                        """, tuple(row_values))
                    except Exception as e:
                        print(f"Error inserting row into option_data_live: {e}")

                conn.commit()
                print(f"Successfully inserted {len(values)} rows into option_data and option_data_live (11 strikes only)")
            except Exception as e:
                print(f"Error in dataframe insertion: {e}")

            # Values already set above in market_data

            # Check if any of the specific cells' values have changed
            if (prev_call_oi_value is not None and prev_call_oi_value != current_call_oi_value) or \
                    (prev_call_oi_change is not None and prev_call_oi_change != current_call_oi_change) or \
                    (prev_put_oi_value is not None and prev_put_oi_value != current_put_oi_value) or \
                    (prev_put_oi_change is not None and prev_put_oi_change != current_put_oi_change):

                # Send message only if any of the values have changed
                message = "{}\nExpiry: {}\n{}: {}!! High: {}!! Low: {}!!\nMax Call OI & OI change: {} & {} !!!!\nMax Put OI & OI change: {} & {} !!!!\nPCR: {}".format(
                    formatted_time, formatted_expiry, " Nifty", nifty_value, today_high, today_low, current_call_oi_value, current_call_oi_change, current_put_oi_value, current_put_oi_change, pcr_value
                )
                # send_telegram_message(message)
                # print(formatted_time,"!!!", "Nifty:", nifty_value,"!!!", "Max Call OI:", current_call_oi_value,"!!!", "Max Call OI Change:", current_call_oi_change,"!!!", "Max Put OI:", current_put_oi_value,"!!!", "Max Put OI Change:", current_put_oi_change)

            # Update previous values for next iteration
            prev_call_oi_value = current_call_oi_value
            prev_call_oi_change = current_call_oi_change
            prev_put_oi_value = current_put_oi_value
            prev_put_oi_change = current_put_oi_change

            # Calculate how much time to sleep to ensure we run exactly once per second
            elapsed_time = time.time() - start_time
            sleep_time = max(0, 1.0 - elapsed_time)  # Ensure we don't sleep for negative time
            if sleep_time > 0:
                time.sleep(sleep_time)

        except Exception as e:
            print(f"Error: {e}")
            pass