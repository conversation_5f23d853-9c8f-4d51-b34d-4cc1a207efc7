{% extends "base.html" %}

{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Welcome, {{ current_user.username }}</h4>
            </div>
            <div class="card-body">
                <p class="lead">Welcome to your dashboard. View and manage your watchlists and live stocks.</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="dashboardTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="watchlist-tab" data-bs-toggle="tab" data-bs-target="#watchlist" type="button" role="tab" aria-controls="watchlist" aria-selected="true">Watchlists</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="live-stocks-tab" data-bs-toggle="tab" data-bs-target="#live-stocks" type="button" role="tab" aria-controls="live-stocks" aria-selected="false">Live Stocks</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="fii-dii-tab" data-bs-toggle="tab" data-bs-target="#fii-dii-tab-content" type="button" role="tab" aria-controls="fii-dii-tab-content" aria-selected="false">FII/DII</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="advance-fii-tab" data-bs-toggle="tab" data-bs-target="#advance-fii-tab-content" type="button" role="tab" aria-controls="advance-fii-tab-content" aria-selected="false">Advance FII</button>
                    </li>
                </ul>
            </div>
            <div class="card-body p-0">
                <div class="tab-content" id="dashboardTabsContent">
                    <!-- Watchlist Tab -->
                    <div class="tab-pane fade show active" id="watchlist" role="tabpanel" aria-labelledby="watchlist-tab">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header watchlist-header">
                                        <h5 class="mb-0">Your Watchlists</h5>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#createWatchlistModal">
                                            <i class="bi bi-plus"></i> New
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group">
                                            {% if watchlists %}
                                                {% for watchlist in watchlists %}
                                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center watchlist-item" data-watchlist-id="{{ watchlist.id }}">
                                                        {{ watchlist.name }}
                                                        <span class="badge bg-primary rounded-pill">{{ watchlist.stocks|length }}</span>
                                                    </a>
                                                {% endfor %}
                                            {% else %}
                                                <div class="text-center py-3">
                                                    <p class="text-muted">No watchlists yet</p>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createWatchlistModal">
                                                        Create your first watchlist
                                                    </button>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card" id="watchlist-card">
                                    <div class="card-header watchlist-header">
                                        <div class="watchlist-title-container d-flex align-items-center">
                                            <h5 class="mb-0 watchlist-title">Select a watchlist</h5>
                                        </div>
                                        <div class="btn-group watchlist-actions" style="display: none;">
                                            <button type="button" class="btn btn-sm btn-primary add-stock-btn" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                                <i class="bi bi-plus"></i> Add Stock
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger delete-watchlist-btn" data-bs-toggle="modal" data-bs-target="#deleteWatchlistModal">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="watchlist-stocks-container">
                                            <div class="text-center py-5 no-watchlist-selected">
                                                <p class="text-muted">Select a watchlist to view stocks</p>
                                            </div>
                                            <div class="watchlist-stocks" style="display: none;">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Symbol</th>
                                                                <th>Last Price</th>
                                                                <th>Change</th>
                                                                <th>Open</th>
                                                                <th>High</th>
                                                                <th>Low</th>
                                                                <th>Close</th>
                                                                <th>Volume</th>
                                                                <th>Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="watchlist-stocks-table">
                                                            <!-- Stock data will be loaded here -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Stocks Tab -->
                    <div class="tab-pane fade" id="live-stocks" role="tabpanel" aria-labelledby="live-stocks-tab">
                        <div class="alert alert-info alert-dismissible fade show mb-3" role="alert">
                            <strong>Live Stocks</strong> - Showing all available stocks with real-time updates. Use the search box to find specific stocks.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" id="stock-search" class="form-control" placeholder="Search for stocks...">
                                    <button class="btn btn-outline-secondary" type="button" id="search-button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <div id="search-results" class="search-results" style="display: none;"></div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary" id="refresh-stocks-btn">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <select class="form-select" id="per-page-select" style="width: auto;">
                                        <option value="10">10 per page</option>
                                        <option value="20" selected>20 per page</option>
                                        <option value="50">50 per page</option>
                                        <option value="100">100 per page</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Last Price</th>
                                        <th>Change %</th>
                                        <th>Open</th>
                                        <th>High</th>
                                        <th>Low</th>
                                        <th>Close</th>
                                        <th>Volume</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="live-stocks-table">
                                    <!-- Live stock data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div id="pagination-info" class="text-muted">
                                    Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> of <span id="total-records">0</span> stocks
                                </div>
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="Stock pagination">
                                    <ul class="pagination justify-content-end" id="pagination-controls">
                                        <!-- Pagination controls will be added here -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- FII/DII Tab -->
                    <div class="tab-pane fade" id="fii-dii-tab-content" role="tabpanel" aria-labelledby="fii-dii-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4 class="mb-0">FII/DII Data Analysis</h4>
                                    </div>
                                    <div class="card-body">
                                        <p class="lead">View and analyze FII (Foreign Institutional Investors) and DII (Domestic Institutional Investors) trading data.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">FII/DII Trading Activity</h5>
                                            <div class="form-group mb-0">
                                                <select id="days-filter" class="form-select">
                                                    <option value="7">Last 7 days</option>
                                                    <option value="15">Last 15 days</option>
                                                    <option value="30" selected>Last 30 days</option>
                                                    <option value="60">Last 60 days</option>
                                                    <option value="90">Last 90 days</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="loading-indicator" class="text-center py-5">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            <p class="mt-2">Loading FII/DII data...</p>
                                        </div>

                                        <div id="fii-dii-content" style="display: none;">
                                            <!-- Summary Cards -->
                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <div class="card bg-light">
                                                        <div class="card-header bg-primary text-white">
                                                            <h5 class="mb-0">FII Summary</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Total Buy (Cr)</h6>
                                                                        <h3 id="fii-total-buy">0</h3>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Total Sell (Cr)</h6>
                                                                        <h3 id="fii-total-sell">0</h3>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Net (Cr)</h6>
                                                                        <h3 id="fii-total-net">0</h3>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Buy (Cr)</h6>
                                                                        <h5 id="fii-avg-buy">0</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Sell (Cr)</h6>
                                                                        <h5 id="fii-avg-sell">0</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Net (Cr)</h6>
                                                                        <h5 id="fii-avg-net">0</h5>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card bg-light">
                                                        <div class="card-header bg-success text-white">
                                                            <h5 class="mb-0">DII Summary</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Total Buy (Cr)</h6>
                                                                        <h3 id="dii-total-buy">0</h3>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Total Sell (Cr)</h6>
                                                                        <h3 id="dii-total-sell">0</h3>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center mb-3">
                                                                        <h6>Net (Cr)</h6>
                                                                        <h3 id="dii-total-net">0</h3>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Buy (Cr)</h6>
                                                                        <h5 id="dii-avg-buy">0</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Sell (Cr)</h6>
                                                                        <h5 id="dii-avg-sell">0</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <div class="text-center">
                                                                        <h6>Avg Net (Cr)</h6>
                                                                        <h5 id="dii-avg-net">0</h5>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Charts -->
                                            <div class="row mb-4">
                                                <div class="col-md-12">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5 class="mb-0">Net Investment Trend</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <canvas id="net-investment-chart" height="300"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row mb-4">
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header bg-primary text-white">
                                                            <h5 class="mb-0">FII Buy vs Sell</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <canvas id="fii-buy-sell-chart" height="300"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header bg-success text-white">
                                                            <h5 class="mb-0">DII Buy vs Sell</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <canvas id="dii-buy-sell-chart" height="300"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Data Table -->
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <h5 class="mb-0">Detailed Data</h5>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="table-responsive">
                                                                <table class="table table-striped table-hover">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>Date</th>
                                                                            <th>FII Buy (Cr)</th>
                                                                            <th>FII Sell (Cr)</th>
                                                                            <th>FII Net (Cr)</th>
                                                                            <th>DII Buy (Cr)</th>
                                                                            <th>DII Sell (Cr)</th>
                                                                            <th>DII Net (Cr)</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody id="fii-dii-table-body">
                                                                        <!-- Data will be loaded here -->
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced FII Tab -->
                    {% include 'dashboard/components/advance_fii_component.html' %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Watchlist Modal -->
<div class="modal fade" id="createWatchlistModal" tabindex="-1" aria-labelledby="createWatchlistModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createWatchlistModalLabel">Create New Watchlist</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('create_watchlist') }}">
                <div class="modal-body">
                    {{ watchlist_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ watchlist_form.name.label(class="form-label") }}
                        {{ watchlist_form.name(class="form-control") }}
                        {% for error in watchlist_form.name.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    {{ watchlist_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<div class="modal fade" id="addStockModal" tabindex="-1" aria-labelledby="addStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStockModalLabel">Add Stock to Watchlist</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="add-stock-form" method="POST">
                <div class="modal-body">
                    {{ add_stock_form.hidden_tag() }}
                    <div class="mb-3">
                        {{ add_stock_form.instrument_key.label(class="form-label") }}
                        <div class="input-group">
                            {{ add_stock_form.instrument_key(class="form-control", id="stock-search-modal") }}
                            <button class="btn btn-outline-secondary" type="button" id="search-button-modal">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <div id="search-results-modal" class="search-results" style="display: none;"></div>
                        <div id="add-stock-error" class="text-danger mt-2" style="display: none;"></div>
                        {% for error in add_stock_form.instrument_key.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="add-stock-submit" class="btn btn-primary">{{ add_stock_form.submit.label.text }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Watchlist Modal -->
<div class="modal fade" id="deleteWatchlistModal" tabindex="-1" aria-labelledby="deleteWatchlistModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteWatchlistModalLabel">Delete Watchlist</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this watchlist? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="delete-watchlist-form" method="POST">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Highlight effect for table rows */
    .highlight-row {
        animation: highlight-fade 1s ease-in-out;
    }

    @keyframes highlight-fade {
        0% { background-color: rgba(0, 123, 255, 0.1); }
        100% { background-color: transparent; }
    }

    /* Smooth transitions for all elements */
    .card-body, .table, .chart-container {
        transition: all 0.3s ease-in-out;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/advance_fii.js') }}"></script>
<script>
    $(document).ready(function() {
        let currentWatchlistId = null;
        let refreshInterval = null;
        let liveStocksRefreshInterval = null;
        let fiiDiiRefreshInterval = null;
        let currentPage = 1;
        let perPage = 20;
        let totalPages = 1;
        let currentSearch = '';
        let isTabActive = false;
        const refreshRate = {{ subscription.features|tojson|safe if subscription else '{"refresh_rate": 60}'|tojson|safe }};

        // Chart objects for FII/DII
        let netInvestmentChart = null;
        let fiiBuySellChart = null;
        let diiBuySellChart = null;

        // Function to format price with color based on change
        function formatPrice(price, change) {
            const className = change >= 0 ? 'stock-up' : 'stock-down';
            const icon = change >= 0 ? '<i class="bi bi-arrow-up"></i>' : '<i class="bi bi-arrow-down"></i>';
            return `<span class="${className}">${price.toFixed(2)} ${icon}</span>`;
        }

        // Function to format change percentage
        function formatChange(change) {
            const className = change >= 0 ? 'stock-up' : 'stock-down';
            const icon = change >= 0 ? '<i class="bi bi-arrow-up"></i>' : '<i class="bi bi-arrow-down"></i>';
            return `<span class="${className}">${change.toFixed(2)}% ${icon}</span>`;
        }

        // Function to generate pagination controls
        function generatePaginationControls(currentPage, totalPages) {
            const paginationControls = $('#pagination-controls');
            paginationControls.empty();

            // Previous button
            paginationControls.append(`
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `);

            // Page numbers
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            // First page if not in range
            if (startPage > 1) {
                paginationControls.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="1">1</a>
                    </li>
                `);

                if (startPage > 2) {
                    paginationControls.append(`
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `);
                }
            }

            // Page numbers in range
            for (let i = startPage; i <= endPage; i++) {
                paginationControls.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            }

            // Last page if not in range
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationControls.append(`
                        <li class="page-item disabled">
                            <a class="page-link" href="#">...</a>
                        </li>
                    `);
                }

                paginationControls.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>
                    </li>
                `);
            }

            // Next button
            paginationControls.append(`
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `);

            // Add click event to pagination links
            $('.page-link').on('click', function(e) {
                e.preventDefault();
                const page = $(this).data('page');
                if (page && page !== currentPage && page >= 1 && page <= totalPages) {
                    currentPage = page;
                    loadAllStocks(currentPage, perPage, currentSearch);
                }
            });
        }

        // Function to update pagination info
        function updatePaginationInfo(page, perPage, totalRecords) {
            const start = (page - 1) * perPage + 1;
            const end = Math.min(page * perPage, totalRecords);

            $('#showing-start').text(totalRecords > 0 ? start : 0);
            $('#showing-end').text(end);
            $('#total-records').text(totalRecords);
        }

        // Function to load all stocks with pagination
        function loadAllStocks(page = 1, itemsPerPage = 20, search = '') {
            // Clear any existing refresh interval
            if (liveStocksRefreshInterval) {
                clearInterval(liveStocksRefreshInterval);
            }

            // Update current values
            currentPage = page;
            perPage = itemsPerPage;
            currentSearch = search;

            // Show loading indicator
            const tableBody = $('#live-stocks-table');
            tableBody.html(`
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading stocks...</p>
                    </td>
                </tr>
            `);

            // Function to update live stocks refresh timer
            function updateLiveStocksRefreshTimer(seconds) {
                // Create timer element if it doesn't exist
                if ($('#live-stocks-refresh-timer').length === 0) {
                    $('#live-stocks .alert-info').append(`<small id="live-stocks-refresh-timer" class="text-muted ms-2">Next refresh in: ${seconds}s</small>`);
                } else {
                    $('#live-stocks-refresh-timer').text(`Next refresh in: ${seconds}s`);
                }
            }

            // Function to fetch and display all stocks
            function fetchAllStocks() {
                $.ajax({
                    url: '/api/stocks',
                    method: 'GET',
                    data: {
                        page: page,
                        per_page: itemsPerPage,
                        search: search
                    },
                    success: function(response) {
                        const stocks = response.stocks;
                        const refreshRateSeconds = 1; // Always refresh every 1 second
                        const pagination = response.pagination;

                        // Update last refreshed time
                        const lastUpdated = new Date().toLocaleTimeString();
                        if ($('#live-stocks-last-updated').length === 0) {
                            // Add last updated element if it doesn't exist
                            $('#live-stocks .alert-info').append(`<small id="live-stocks-last-updated" class="text-muted ms-2">Last updated: ${lastUpdated}</small>`);
                        } else {
                            $('#live-stocks-last-updated').text(`Last updated: ${lastUpdated}`);
                        }

                        // Update refresh timer
                        updateLiveStocksRefreshTimer(1);

                        // Update pagination variables
                        totalPages = pagination.total_pages;

                        // Update pagination controls
                        generatePaginationControls(pagination.page, pagination.total_pages);

                        // Update pagination info
                        updatePaginationInfo(pagination.page, pagination.per_page, pagination.total_records);

                        // Update the table
                        tableBody.empty();

                        if (stocks.length === 0) {
                            tableBody.append(`
                                <tr>
                                    <td colspan="9" class="text-center">
                                        ${search ? `No stocks found matching "${search}".` : 'No stocks available.'}
                                    </td>
                                </tr>
                            `);
                        } else {
                            stocks.forEach(stock => {
                                tableBody.append(`
                                    <tr>
                                        <td>${stock.instrument_key}</td>
                                        <td>${formatPrice(stock.last_price, stock.change_percent)}</td>
                                        <td>${formatChange(stock.change_percent)}</td>
                                        <td>${stock.live_open ? stock.live_open.toFixed(2) : 'N/A'}</td>
                                        <td>${stock.live_high ? stock.live_high.toFixed(2) : 'N/A'}</td>
                                        <td>${stock.live_low ? stock.live_low.toFixed(2) : 'N/A'}</td>
                                        <td>${stock.live_close ? stock.live_close.toFixed(2) : 'N/A'}</td>
                                        <td>${stock.live_volume || 'N/A'}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary add-to-watchlist-btn" data-instrument-key="${stock.instrument_key}">
                                                <i class="bi bi-plus"></i> Add to Watchlist
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            });
                        }

                        // Set up the next refresh
                        if (liveStocksRefreshInterval) {
                            clearInterval(liveStocksRefreshInterval);
                        }
                        // Always use 1 second refresh rate for smooth updates
                        liveStocksRefreshInterval = setInterval(fetchAllStocks, 1000);
                    },
                    error: function(error) {
                        console.error('Error fetching stocks:', error);
                        tableBody.html(`
                            <tr>
                                <td colspan="9" class="text-center text-danger">
                                    <i class="bi bi-exclamation-triangle"></i> Error loading stocks. Please try again.
                                </td>
                            </tr>
                        `);
                    }
                });
            }

            // Initial fetch
            fetchAllStocks();
        }

        // Function to update watchlist refresh timer
        function updateWatchlistRefreshTimer(seconds) {
            // Create timer element if it doesn't exist
            if ($('#watchlist-refresh-timer').length === 0) {
                $('.watchlist-title-container').append(`<small id="watchlist-refresh-timer" class="text-muted ms-2">Next refresh in: ${seconds}s</small>`);
            } else {
                $('#watchlist-refresh-timer').text(`Next refresh in: ${seconds}s`);
            }
        }

        // Function to load watchlist stocks
        function loadWatchlistStocks(watchlistId) {
            if (!watchlistId) return;

            currentWatchlistId = watchlistId;

            // Clear any existing refresh interval
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }

            // Function to fetch and display watchlist stocks
            function fetchWatchlistStocks() {
                $.ajax({
                    url: `/api/watchlist/${watchlistId}/stocks`,
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        const stocks = response.stocks;
                        const refreshRateSeconds = 1; // Always refresh every 1 second

                        console.log('Watchlist stocks response:', response);
                        console.log('Stock IDs:', stocks.map(stock => stock.id));

                        // Update the table
                        const tableBody = $('#watchlist-stocks-table');
                        tableBody.empty();

                        // Update last refreshed time
                        const lastUpdated = new Date().toLocaleTimeString();
                        if ($('#watchlist-last-updated').length === 0) {
                            // Add last updated element if it doesn't exist
                            $('#watchlist-card .card-header').append(`<small id="watchlist-last-updated" class="text-muted ms-2">Last updated: ${lastUpdated}</small>`);
                        } else {
                            $('#watchlist-last-updated').text(`Last updated: ${lastUpdated}`);
                        }

                        // Update refresh timer
                        updateWatchlistRefreshTimer(1);

                        if (stocks.length === 0) {
                            tableBody.append(`
                                <tr>
                                    <td colspan="9" class="text-center">No stocks in this watchlist</td>
                                </tr>
                            `);
                        } else {
                            stocks.forEach(stock => {
                                const change = ((stock.last_price - stock.prev_close) / stock.prev_close) * 100;

                                tableBody.append(`
                                    <tr>
                                        <td>${stock.instrument_key}</td>
                                        <td>${formatPrice(stock.last_price, change)}</td>
                                        <td>${formatChange(change)}</td>
                                        <td>${stock.live_open.toFixed(2)}</td>
                                        <td>${stock.live_high.toFixed(2)}</td>
                                        <td>${stock.live_low.toFixed(2)}</td>
                                        <td>${stock.live_close.toFixed(2)}</td>
                                        <td>${stock.live_volume}</td>
                                        <td>
                                            <!-- Stock ID: ${stock.id} -->
                                            <button type="button" class="btn btn-sm btn-danger delete-stock-btn" data-stock-id="${stock.id}" onclick="console.log('Delete button for stock ID: ${stock.id}')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `);
                            });
                        }

                        // Set up the next refresh
                        if (refreshInterval) {
                            clearInterval(refreshInterval);
                        }
                        // Always use 1 second refresh rate for smooth updates
                        refreshInterval = setInterval(fetchWatchlistStocks, 1000);
                    },
                    error: function(error) {
                        console.error('Error fetching watchlist stocks:', error);
                    }
                });
            }

            // Initial fetch
            fetchWatchlistStocks();

            // Update UI to show the selected watchlist
            $('.watchlist-item').removeClass('active');
            $(`.watchlist-item[data-watchlist-id="${watchlistId}"]`).addClass('active');

            // Update the watchlist title and show actions
            const watchlistName = $(`.watchlist-item[data-watchlist-id="${watchlistId}"]`).text().trim();
            $('.watchlist-title').text(watchlistName);
            $('.watchlist-actions').show();

            // Update the add stock form action
            $('#add-stock-form').attr('action', `/watchlist/${watchlistId}/add_stock`);

            // Update the delete watchlist form action
            $('#delete-watchlist-form').attr('action', `/watchlist/${watchlistId}/delete`);

            // Show the stocks table and hide the no selection message
            $('.no-watchlist-selected').hide();
            $('.watchlist-stocks').show();
        }

        // Handle watchlist selection
        $('.watchlist-item').on('click', function(e) {
            e.preventDefault();
            const watchlistId = $(this).data('watchlist-id');
            loadWatchlistStocks(watchlistId);
        });

        // Function to search for stocks
        function searchStocks(query, resultsContainer) {
            if (query.length < 2) {
                resultsContainer.hide();
                return;
            }

            $.ajax({
                url: `/api/stocks/search?q=${encodeURIComponent(query)}`,
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(stocks) {
                    resultsContainer.empty();

                    if (stocks.length === 0) {
                        resultsContainer.append(`
                            <div class="search-result-item">No stocks found</div>
                        `);
                    } else {
                        stocks.forEach(stock => {
                            resultsContainer.append(`
                                <div class="search-result-item" data-instrument-key="${stock.instrument_key}">
                                    ${stock.instrument_key} - ${stock.last_price}
                                </div>
                            `);
                        });
                    }

                    resultsContainer.show();
                },
                error: function(error) {
                    console.error('Error searching stocks:', error);
                }
            });
        }

        // Handle stock search in the main tab
        $('#stock-search').on('input', function() {
            const query = $(this).val();
            if (query.length >= 2) {
                currentSearch = query;
                loadAllStocks(1, perPage, query);
            } else if (query.length === 0 && currentSearch !== '') {
                currentSearch = '';
                loadAllStocks(1, perPage, '');
            }
        });

        // Handle search button click
        $('#search-button').on('click', function() {
            const query = $('#stock-search').val();
            if (query !== currentSearch) {
                currentSearch = query;
                loadAllStocks(1, perPage, query);
            }
        });

        // Handle refresh button click
        $('#refresh-stocks-btn').on('click', function() {
            loadAllStocks(currentPage, perPage, currentSearch);
        });

        // Handle per page select change
        $('#per-page-select').on('change', function() {
            perPage = parseInt($(this).val());
            loadAllStocks(1, perPage, currentSearch);
        });

        // Handle stock search in the modal
        $('#stock-search-modal').on('input', function() {
            const query = $(this).val();
            searchStocks(query, $('#search-results-modal'));
        });

        // Handle search result selection in the modal
        $(document).on('click', '#search-results-modal .search-result-item', function() {
            const instrumentKey = $(this).data('instrument-key');
            $('#stock-search-modal').val(instrumentKey);
            $('#search-results-modal').hide();
        });

        // Handle "Add to Watchlist" button click
        $(document).on('click', '.add-to-watchlist-btn', function() {
            const instrumentKey = $(this).data('instrument-key');

            // Show the add stock modal and pre-fill the instrument key
            $('#stock-search-modal').val(instrumentKey);
            $('#addStockModal').modal('show');
        });

        // Handle add stock form submission
        $('#add-stock-submit').on('click', function() {
            if (!currentWatchlistId) {
                $('#add-stock-error').text('Please select a watchlist first').show();
                return;
            }

            const instrumentKey = $('#stock-search-modal').val();
            if (!instrumentKey) {
                $('#add-stock-error').text('Please enter a stock symbol').show();
                return;
            }

            // Clear previous errors
            $('#add-stock-error').hide();

            // Get the CSRF token from the form
            const csrfToken = $('#add-stock-form input[name="csrf_token"]').val();

            // Submit the form via AJAX
            $.ajax({
                url: `/watchlist/${currentWatchlistId}/add_stock`,
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: {
                    csrf_token: csrfToken,
                    instrument_key: instrumentKey
                },
                success: function(response) {
                    console.log('Add success:', response);
                    // Close the modal
                    $('#addStockModal').modal('hide');

                    // Clear the form
                    $('#stock-search-modal').val('');

                    // Show success message
                    alert('Stock added to watchlist successfully!');

                    // Refresh the watchlist
                    loadWatchlistStocks(currentWatchlistId);
                },
                error: function(xhr, status, error) {
                    console.error('Add error:', xhr.responseText, status, error);
                    // Show error message
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        $('#add-stock-error').text(xhr.responseJSON.error).show();
                    } else {
                        $('#add-stock-error').text('Failed to add stock to watchlist. Please try again.').show();
                    }
                }
            });
        });

        // Handle delete stock button click
        $(document).on('click', '.delete-stock-btn', function() {
            if (!currentWatchlistId) {
                console.error('No watchlist selected');
                return;
            }

            const stockId = $(this).data('stock-id');
            console.log('Delete button clicked for stock ID:', stockId);

            if (!stockId) {
                console.error('No stock ID found');
                return;
            }

            if (confirm('Are you sure you want to remove this stock from your watchlist?')) {
                // Get the CSRF token from the form
                const csrfToken = $('#add-stock-form input[name="csrf_token"]').val();
                console.log('CSRF Token:', csrfToken);
                console.log(`Sending delete request to: /watchlist/${currentWatchlistId}/remove_stock/${stockId}`);

                // Submit the delete request via AJAX
                $.ajax({
                    url: `/watchlist/${currentWatchlistId}/remove_stock/${stockId}`,
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    data: {
                        csrf_token: csrfToken
                    },
                    success: function(response) {
                        console.log('Delete success:', response);
                        // Show success message
                        alert('Stock removed from watchlist successfully!');

                        // Refresh the watchlist
                        loadWatchlistStocks(currentWatchlistId);
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete error:', xhr.responseText, status, error);
                        console.error('Status:', status);
                        console.error('Error:', error);
                        // Show error message
                        alert('Failed to remove stock from watchlist. Please try again.');
                    }
                });
            }
        });

        // Hide search results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#stock-search, #search-results, #stock-search-modal, #search-results-modal').length) {
                $('#search-results, #search-results-modal').hide();
            }
        });

        // FII/DII Functions

        // Function to format numbers with commas and 2 decimal places
        function formatNumber(num) {
            return new Intl.NumberFormat('en-IN', {
                maximumFractionDigits: 2,
                minimumFractionDigits: 2
            }).format(num);
        }

        // Function to set color based on value
        function getValueColor(value) {
            return value >= 0 ? 'text-success' : 'text-danger';
        }

        // Function to smoothly update text with animation
        function smoothUpdateText(selector, newValue) {
            const element = $(selector);
            const oldValue = element.text();

            if (oldValue !== newValue) {
                element.fadeOut(100, function() {
                    $(this).text(newValue).fadeIn(100);
                });
            }
        }

        // Function to load FII/DII data
        function loadFiiDiiData(days = 30) {
            // Only show loading indicator on first load
            if ($('#fii-dii-content').css('display') === 'none') {
                $('#loading-indicator').show();
            }

            console.log("Loading FII/DII data for " + days + " days");

            $.ajax({
                url: '/api/fii-dii',
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                data: { days: days },
                success: function(response) {
                    console.log("FII/DII API response:", response);
                    // Update summary cards with smooth transitions
                    smoothUpdateText('#fii-total-buy', formatNumber(response.fii_summary.total_buy));
                    smoothUpdateText('#fii-total-sell', formatNumber(response.fii_summary.total_sell));
                    smoothUpdateText('#fii-total-net', formatNumber(response.fii_summary.total_net));
                    $('#fii-total-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.fii_summary.total_net));

                    smoothUpdateText('#fii-avg-buy', formatNumber(response.fii_summary.avg_buy));
                    smoothUpdateText('#fii-avg-sell', formatNumber(response.fii_summary.avg_sell));
                    smoothUpdateText('#fii-avg-net', formatNumber(response.fii_summary.avg_net));
                    $('#fii-avg-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.fii_summary.avg_net));

                    smoothUpdateText('#dii-total-buy', formatNumber(response.dii_summary.total_buy));
                    smoothUpdateText('#dii-total-sell', formatNumber(response.dii_summary.total_sell));
                    smoothUpdateText('#dii-total-net', formatNumber(response.dii_summary.total_net));
                    $('#dii-total-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.dii_summary.total_net));

                    smoothUpdateText('#dii-avg-buy', formatNumber(response.dii_summary.avg_buy));
                    smoothUpdateText('#dii-avg-sell', formatNumber(response.dii_summary.avg_sell));
                    smoothUpdateText('#dii-avg-net', formatNumber(response.dii_summary.avg_net));
                    $('#dii-avg-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.dii_summary.avg_net));

                    // Prepare data for charts
                    const dates = response.dates.reverse(); // Reverse to show oldest to newest

                    // Create a map of FII data by date
                    const fiiDataMap = {};
                    response.fii_data.forEach(item => {
                        fiiDataMap[item.date] = item;
                    });

                    // Create a map of DII data by date
                    const diiDataMap = {};
                    response.dii_data.forEach(item => {
                        diiDataMap[item.date] = item;
                    });

                    // Prepare data for charts
                    const fiiNetValues = [];
                    const diiNetValues = [];
                    const fiiBuyValues = [];
                    const fiiSellValues = [];
                    const diiBuyValues = [];
                    const diiSellValues = [];

                    dates.forEach(date => {
                        const fiiData = fiiDataMap[date] || { net_value: 0, buy_value: 0, sell_value: 0 };
                        const diiData = diiDataMap[date] || { net_value: 0, buy_value: 0, sell_value: 0 };

                        fiiNetValues.push(fiiData.net_value);
                        diiNetValues.push(diiData.net_value);
                        fiiBuyValues.push(fiiData.buy_value);
                        fiiSellValues.push(fiiData.sell_value);
                        diiBuyValues.push(diiData.buy_value);
                        diiSellValues.push(diiData.sell_value);
                    });

                    // Update charts
                    updateNetInvestmentChart(dates, fiiNetValues, diiNetValues);
                    updateFiiBuySellChart(dates, fiiBuyValues, fiiSellValues);
                    updateDiiBuySellChart(dates, diiBuyValues, diiSellValues);

                    // Update table
                    updateFiiDiiTable(dates, fiiDataMap, diiDataMap);

                    // Hide loading indicator and show content
                    $('#loading-indicator').hide();
                    $('#fii-dii-content').show();

                    // Reset the refresh timer
                    if (fiiDiiRefreshInterval) {
                        updateFiiDiiRefreshTimer(300);
                    }
                },
                error: function(error) {
                    console.error('Error fetching FII/DII data:', error);
                    $('#loading-indicator').hide();
                    // Only show alert if this is the first load
                    if ($('#fii-dii-content').css('display') === 'none') {
                        alert('Error loading FII/DII data. Please try again later.');
                    }
                }
            });
        }

        // Function to update Net Investment Chart
        function updateNetInvestmentChart(dates, fiiNetValues, diiNetValues) {
            const ctx = document.getElementById('net-investment-chart').getContext('2d');

            if (!netInvestmentChart) {
                // Create chart if it doesn't exist
                netInvestmentChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: dates,
                        datasets: [
                            {
                                label: 'FII Net Investment',
                                data: fiiNetValues,
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderWidth: 2,
                                tension: 0.1,
                                fill: true
                            },
                            {
                                label: 'DII Net Investment',
                                data: diiNetValues,
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                borderWidth: 2,
                                tension: 0.1,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        animation: {
                            duration: 500 // Smooth animation duration
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'FII vs DII Net Investment Trend (in Cr)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += formatNumber(context.raw);
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: 'Amount (Cr)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        }
                    }
                });
            } else {
                // Update existing chart data
                netInvestmentChart.data.labels = dates;
                netInvestmentChart.data.datasets[0].data = fiiNetValues;
                netInvestmentChart.data.datasets[1].data = diiNetValues;
                // Update with minimal animation for smooth refresh
                netInvestmentChart.update({
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            }
        }

        // Function to update FII Buy vs Sell Chart
        function updateFiiBuySellChart(dates, buyValues, sellValues) {
            const ctx = document.getElementById('fii-buy-sell-chart').getContext('2d');

            if (!fiiBuySellChart) {
                // Create chart if it doesn't exist
                fiiBuySellChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: dates,
                        datasets: [
                            {
                                label: 'Buy',
                                data: buyValues,
                                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Sell',
                                data: sellValues,
                                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        animation: {
                            duration: 500 // Smooth animation duration
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'FII Buy vs Sell (in Cr)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += formatNumber(context.raw);
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: 'Amount (Cr)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        }
                    }
                });
            } else {
                // Update existing chart data
                fiiBuySellChart.data.labels = dates;
                fiiBuySellChart.data.datasets[0].data = buyValues;
                fiiBuySellChart.data.datasets[1].data = sellValues;
                // Update with minimal animation for smooth refresh
                fiiBuySellChart.update({
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            }
        }

        // Function to update DII Buy vs Sell Chart
        function updateDiiBuySellChart(dates, buyValues, sellValues) {
            const ctx = document.getElementById('dii-buy-sell-chart').getContext('2d');

            if (!diiBuySellChart) {
                // Create chart if it doesn't exist
                diiBuySellChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: dates,
                        datasets: [
                            {
                                label: 'Buy',
                                data: buyValues,
                                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Sell',
                                data: sellValues,
                                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        animation: {
                            duration: 500 // Smooth animation duration
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'DII Buy vs Sell (in Cr)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += formatNumber(context.raw);
                                        return label;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: 'Amount (Cr)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        }
                    }
                });
            } else {
                // Update existing chart data
                diiBuySellChart.data.labels = dates;
                diiBuySellChart.data.datasets[0].data = buyValues;
                diiBuySellChart.data.datasets[1].data = sellValues;
                // Update with minimal animation for smooth refresh
                diiBuySellChart.update({
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            }
        }

        // Function to update FII/DII data table
        function updateFiiDiiTable(dates, fiiDataMap, diiDataMap) {
            const tableBody = $('#fii-dii-table-body');

            // Create a map of existing rows by date for quick lookup
            const existingRows = {};
            tableBody.find('tr').each(function() {
                const date = $(this).find('td:first').text();
                existingRows[date] = $(this);
            });

            // Create a document fragment to hold new rows
            const fragment = document.createDocumentFragment();
            let rowsChanged = false;

            dates.forEach(date => {
                const fiiData = fiiDataMap[date] || { buy_value: 0, sell_value: 0, net_value: 0 };
                const diiData = diiDataMap[date] || { buy_value: 0, sell_value: 0, net_value: 0 };

                const fiiNetClass = fiiData.net_value >= 0 ? 'text-success' : 'text-danger';
                const diiNetClass = diiData.net_value >= 0 ? 'text-success' : 'text-danger';

                if (existingRows[date]) {
                    // Update existing row
                    const row = existingRows[date];
                    const cells = row.find('td');

                    // Only update if values have changed
                    const currentFiiBuy = cells.eq(1).text();
                    const newFiiBuy = formatNumber(fiiData.buy_value);
                    if (currentFiiBuy !== newFiiBuy) {
                        cells.eq(1).fadeOut(100, function() {
                            $(this).text(newFiiBuy).fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    const currentFiiSell = cells.eq(2).text();
                    const newFiiSell = formatNumber(fiiData.sell_value);
                    if (currentFiiSell !== newFiiSell) {
                        cells.eq(2).fadeOut(100, function() {
                            $(this).text(newFiiSell).fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    const currentFiiNet = cells.eq(3).text();
                    const newFiiNet = formatNumber(fiiData.net_value);
                    if (currentFiiNet !== newFiiNet) {
                        cells.eq(3).fadeOut(100, function() {
                            $(this).removeClass('text-success text-danger')
                                  .addClass(fiiNetClass)
                                  .text(newFiiNet)
                                  .fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    const currentDiiBuy = cells.eq(4).text();
                    const newDiiBuy = formatNumber(diiData.buy_value);
                    if (currentDiiBuy !== newDiiBuy) {
                        cells.eq(4).fadeOut(100, function() {
                            $(this).text(newDiiBuy).fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    const currentDiiSell = cells.eq(5).text();
                    const newDiiSell = formatNumber(diiData.sell_value);
                    if (currentDiiSell !== newDiiSell) {
                        cells.eq(5).fadeOut(100, function() {
                            $(this).text(newDiiSell).fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    const currentDiiNet = cells.eq(6).text();
                    const newDiiNet = formatNumber(diiData.net_value);
                    if (currentDiiNet !== newDiiNet) {
                        cells.eq(6).fadeOut(100, function() {
                            $(this).removeClass('text-success text-danger')
                                  .addClass(diiNetClass)
                                  .text(newDiiNet)
                                  .fadeIn(100);
                        });
                        rowsChanged = true;
                    }

                    // Mark this row as processed
                    delete existingRows[date];
                } else {
                    // Create new row
                    const newRow = document.createElement('tr');
                    newRow.innerHTML = `
                        <td>${date}</td>
                        <td>${formatNumber(fiiData.buy_value)}</td>
                        <td>${formatNumber(fiiData.sell_value)}</td>
                        <td class="${fiiNetClass}">${formatNumber(fiiData.net_value)}</td>
                        <td>${formatNumber(diiData.buy_value)}</td>
                        <td>${formatNumber(diiData.sell_value)}</td>
                        <td class="${diiNetClass}">${formatNumber(diiData.net_value)}</td>
                    `;
                    fragment.appendChild(newRow);
                    rowsChanged = true;
                }
            });

            // Remove rows that are no longer in the data
            for (const date in existingRows) {
                existingRows[date].fadeOut(300, function() {
                    $(this).remove();
                });
                rowsChanged = true;
            }

            // Add new rows
            if (fragment.childNodes.length > 0) {
                tableBody.append(fragment);
                $(fragment.childNodes).hide().fadeIn(300);
            }

            // If no changes were made, add a subtle highlight effect to show data is fresh
            if (!rowsChanged) {
                tableBody.find('tr:first').addClass('highlight-row');
                setTimeout(function() {
                    tableBody.find('tr').removeClass('highlight-row');
                }, 500);
            }
        }

        // Function to start FII/DII auto-refresh
        function startFiiDiiAutoRefresh() {
            if (!fiiDiiRefreshInterval) {
                // Refresh every 5 minutes (300,000 ms)
                fiiDiiRefreshInterval = setInterval(function() {
                    const days = $('#days-filter').val();
                    loadFiiDiiData(days);
                }, 300000);
                console.log('FII/DII auto-refresh started (every 5 minutes)');

                // Update the refresh timer
                updateFiiDiiRefreshTimer(300);
            }
        }

        // Function to update FII/DII refresh timer
        function updateFiiDiiRefreshTimer(seconds) {
            // Create timer element if it doesn't exist
            if ($('#fii-dii-refresh-timer').length === 0) {
                $('#fii-dii-tab-content .card-header:first').append(`<small id="fii-dii-refresh-timer" class="text-muted ms-2">Next refresh in: ${seconds}s</small>`);
            }

            // Update the timer every second
            let remainingSeconds = seconds;
            const timerInterval = setInterval(function() {
                remainingSeconds--;
                if (remainingSeconds <= 0) {
                    clearInterval(timerInterval);
                    $('#fii-dii-refresh-timer').text('Refreshing...');
                } else {
                    $('#fii-dii-refresh-timer').text(`Next refresh in: ${remainingSeconds}s`);
                }
            }, 1000);
        }

        // Function to stop FII/DII auto-refresh
        function stopFiiDiiAutoRefresh() {
            if (fiiDiiRefreshInterval) {
                clearInterval(fiiDiiRefreshInterval);
                fiiDiiRefreshInterval = null;
                console.log('FII/DII auto-refresh stopped');
            }
        }

        // Handle days filter change for FII/DII
        $('#days-filter').on('change', function() {
            const days = $(this).val();
            loadFiiDiiData(days);
        });

        // Handle tab changes
        $('#dashboardTabs .nav-link').on('click', function() {
            // Clear all refresh intervals
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }

            if (liveStocksRefreshInterval) {
                clearInterval(liveStocksRefreshInterval);
                liveStocksRefreshInterval = null;
            }

            if (fiiDiiRefreshInterval) {
                clearInterval(fiiDiiRefreshInterval);
                fiiDiiRefreshInterval = null;
            }

            // Load appropriate data based on the selected tab
            if ($(this).attr('id') === 'live-stocks-tab') {
                loadAllStocks(currentPage, perPage, currentSearch);
            } else if ($(this).attr('id') === 'watchlist-tab') {
                // If a watchlist is already selected, reload it
                if (currentWatchlistId) {
                    loadWatchlistStocks(currentWatchlistId);
                }
            } else if ($(this).attr('id') === 'fii-dii-tab') {
                // Load FII/DII data
                loadFiiDiiData(30);
                startFiiDiiAutoRefresh();
            }
        });

        // Load the first watchlist by default if available
        const firstWatchlist = $('.watchlist-item').first();
        if (firstWatchlist.length) {
            firstWatchlist.click();
        }

        // Automatically switch to the Live Stocks tab and load data
        $('#live-stocks-tab').tab('show');
        loadAllStocks(1, perPage, '');
    });
</script>
{% endblock %}
