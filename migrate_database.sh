#!/bin/bash

# MySQL connection details
HOST="localhost"
USER="root"
PASSWORD="vinayak123"
SOURCE_DB="option_Chain_data"
TARGET_DB="option_chain_db"

# Function to check if a command was successful
check_success() {
    if [ $? -ne 0 ]; then
        echo "Error: $1"
        exit 1
    fi
}

echo "Starting database migration from $SOURCE_DB to $TARGET_DB..."

# Get list of tables from source database
TABLES=$(mysql -h$HOST -u$USER -p$PASSWORD -e "SHOW TABLES FROM $SOURCE_DB;" | grep -v "Tables_in")
check_success "Failed to get tables from source database"

echo "Found tables in source database: $TABLES"

# Process each table
for TABLE in $TABLES; do
    echo "Processing table: $TABLE"
    
    # Check if table exists in target database
    TABLE_EXISTS=$(mysql -h$HOST -u$USER -p$PASSWORD -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$TARGET_DB' AND table_name='$TABLE';" | grep -v "COUNT")
    
    if [ "$TABLE_EXISTS" -eq "1" ]; then
        echo "Table $TABLE already exists in target database."
        read -p "Do you want to drop and recreate $TABLE? (y/n): " RESPONSE
        if [ "$RESPONSE" = "y" ]; then
            mysql -h$HOST -u$USER -p$PASSWORD $TARGET_DB -e "DROP TABLE $TABLE;"
            check_success "Failed to drop table $TABLE"
            echo "Dropped table $TABLE from target database."
        else
            echo "Skipping table $TABLE."
            continue
        fi
    fi
    
    # Get CREATE TABLE statement and create table in target database
    CREATE_STATEMENT=$(mysql -h$HOST -u$USER -p$PASSWORD -e "SHOW CREATE TABLE $SOURCE_DB.$TABLE\G" | grep "Create Table" | sed 's/Create Table: //')
    mysql -h$HOST -u$USER -p$PASSWORD $TARGET_DB -e "$CREATE_STATEMENT"
    check_success "Failed to create table $TABLE in target database"
    echo "Created table $TABLE in target database."
    
    # Count rows in source table
    ROW_COUNT=$(mysql -h$HOST -u$USER -p$PASSWORD -e "SELECT COUNT(*) FROM $SOURCE_DB.$TABLE;" | grep -v "COUNT")
    
    if [ "$ROW_COUNT" -eq "0" ]; then
        echo "Table $TABLE is empty. No data to migrate."
        continue
    fi
    
    echo "Migrating $ROW_COUNT rows from $TABLE..."
    
    # Migrate data
    mysql -h$HOST -u$USER -p$PASSWORD -e "INSERT INTO $TARGET_DB.$TABLE SELECT * FROM $SOURCE_DB.$TABLE;"
    check_success "Failed to migrate data for table $TABLE"
    
    # Verify row count in target table
    TARGET_ROW_COUNT=$(mysql -h$HOST -u$USER -p$PASSWORD -e "SELECT COUNT(*) FROM $TARGET_DB.$TABLE;" | grep -v "COUNT")
    
    echo "Migration complete for $TABLE. Source: $ROW_COUNT rows, Target: $TARGET_ROW_COUNT rows."
    
    if [ "$ROW_COUNT" -ne "$TARGET_ROW_COUNT" ]; then
        echo "WARNING: Row count mismatch for $TABLE!"
    fi
done

echo "Database migration completed."
