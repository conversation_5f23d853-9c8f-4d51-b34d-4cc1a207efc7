{% extends "base.html" %}

{% block title %}Subscriptions{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Subscription Plans</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for subscription in subscriptions %}
                        <div class="col-md-4">
                            <div class="card subscription-card mb-4 {% if current_user.subscription_type == subscription.name %}selected{% endif %}">
                                <div class="card-header text-center">
                                    <h5 class="mb-0">{{ subscription.name|capitalize }}</h5>
                                </div>
                                <div class="card-body">
                                    <h3 class="text-center mb-4">${{ subscription.price }}<small class="text-muted">/month</small></h3>

                                    {% set features_dict = subscription.features %}

                                    <ul class="list-group list-group-flush mb-4">
                                        <li class="list-group-item">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            {{ features_dict.max_watchlists }} watchlists
                                        </li>
                                        <li class="list-group-item">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            {{ features_dict.max_stocks_per_watchlist }} stocks per watchlist
                                        </li>
                                        <li class="list-group-item">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            {{ features_dict.refresh_rate }} second refresh rate
                                        </li>
                                    </ul>

                                    <div class="d-grid">
                                        {% if current_user.subscription_type == subscription.name %}
                                            <button class="btn btn-success" disabled>Current Plan</button>
                                        {% else %}
                                            <form method="POST" action="{{ url_for('subscriptions') }}">
                                                {{ form.hidden_tag() }}
                                                {{ form.plan(value=subscription.name, style="display: none;") }}
                                                {{ form.submit(class="btn btn-primary", value="Subscribe") }}
                                            </form>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Your Subscription</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Current Plan: <span class="badge bg-primary">{{ current_user.subscription_type|capitalize }}</span></h5>
                        <p>
                            {% if current_user.subscription_expiry %}
                                Expires on: {{ current_user.subscription_expiry.strftime('%Y-%m-%d') }}
                            {% else %}
                                No active subscription
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
