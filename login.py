def get_login_credentials():
    global login_credential

    def login_credentials():
        print("---- Enter you <PERSON><PERSON> Login Credentials  ----")
        login_credential = {"api_key": str(input("Enter API Key : ").strip()),
                            "api_secret": str(input("Enter API Secret : ")).strip()}
        if input("Press Y to save login credential and any key to bypass : ").strip().upper() == "Y":
            with open(f"zerodha_login_details.json", "w") as f:
                json.dump(login_credential, f)
            print("Data Saved...")
        else:
            print("Data Save canceled!!!!!")

    while True:
        try:
            with open(f"zerodha_login_details.json", "r") as f:
                login_credential = json.load(f)
            break
        except:
            login_credentials()
    return login_credential


def get_access_token():
    global login_credential, access_token

    def login():
        global login_credential, access_token
        print("Trying Log In...")
        if login_credential["api_key"] == "OptionTechAnalysis":
            print("Login url : ", "https://kite.zerodha.com ( Don't Login Anywhere else after this, Instead of mobile App. )")
            #access_token = input("Login and enter your 'enctoken' here : ")
            token =  'IDJZYPHLUYZMHNRACK2V3KBKVQAQJVRW'                         # Enter Totp
            totp = pyotp.TOTP(token).now()
            #print("Totp is : "+totp)

            # # First Way to Login
            # # You can use your Kite app in mobile
            # # But You can't login anywhere in 'kite.zerodha.com' website else this session will disconnected

            user_id = "PXZ258"       # Login Id
            password = "shiv@331$"      # Login password
            twofa = totp         # Login Pin or TOTP nothing to enter

            access_token= get_enctoken(user_id, password, twofa)
        else:
            kite = KiteConnect(api_key=login_credential["api_key"])
            print("Login url : ", kite.login_url())
            request_tkn = input("Login and enter your 'request_token' here : ")
            try:
                access_token = kite.generate_session(request_token=request_tkn, api_secret=login_credential["api_secret"])['access_token']
            except Exception as e:
                print(f"Login Failed {e}!!!!!")
        os.makedirs(f"AccessToken", exist_ok=True)
        print(access_token)
        with open(f"AccessToken/{datetime.datetime.now().date()}.json", "w") as f:
            json.dump(access_token, f)

    while True:
        if os.path.exists(f"AccessToken/{datetime.datetime.now().date()}.json"):
            with open(f"AccessToken/{datetime.datetime.now().date()}.json", "r") as f:
                access_token = json.load(f)
            break
        else:
            login()
    return access_token