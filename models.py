import datetime
from werkzeug.security import generate_password_hash, check_password_hash
import pymysql
import configparser
import os

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Function to get database connection
def get_db_connection():
    mysql_config = get_mysql_config()
    return pymysql.connect(
        host=mysql_config['host'],
        port=int(mysql_config['port']),
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=mysql_config['database'],
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

# User model
class User:
    def __init__(self, id=None, username=None, email=None, password_hash=None, role='basic',
                 subscription_type=None, subscription_expiry=None, created_at=None, is_active=True):
        self.id = id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.role = role  # 'admin', 'premium', 'basic'
        self.subscription_type = subscription_type  # 'free', 'basic', 'premium'
        self.subscription_expiry = subscription_expiry
        self.created_at = created_at or datetime.datetime.now()
        self.is_active = is_active
        self.is_authenticated = True
        self.is_anonymous = False

    def get_id(self):
        return str(self.id)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_subscription_active(self):
        if not self.subscription_expiry:
            return False
        return datetime.datetime.now() < self.subscription_expiry

    @staticmethod
    def get_by_id(user_id):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
            user_data = cursor.fetchone()
            if user_data:
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash'],
                    role=user_data['role'],
                    subscription_type=user_data['subscription_type'],
                    subscription_expiry=user_data['subscription_expiry'],
                    created_at=user_data['created_at'],
                    is_active=user_data['is_active']
                )
            return None
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_by_email(email):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM users WHERE email = %s", (email,))
            user_data = cursor.fetchone()
            if user_data:
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash'],
                    role=user_data['role'],
                    subscription_type=user_data['subscription_type'],
                    subscription_expiry=user_data['subscription_expiry'],
                    created_at=user_data['created_at'],
                    is_active=user_data['is_active']
                )
            return None
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_by_username(username):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
            user_data = cursor.fetchone()
            if user_data:
                return User(
                    id=user_data['id'],
                    username=user_data['username'],
                    email=user_data['email'],
                    password_hash=user_data['password_hash'],
                    role=user_data['role'],
                    subscription_type=user_data['subscription_type'],
                    subscription_expiry=user_data['subscription_expiry'],
                    created_at=user_data['created_at'],
                    is_active=user_data['is_active']
                )
            return None
        finally:
            cursor.close()
            conn.close()

    def save(self):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            if self.id:
                # Update existing user
                cursor.execute("""
                UPDATE users SET
                    username = %s,
                    email = %s,
                    password_hash = %s,
                    role = %s,
                    subscription_type = %s,
                    subscription_expiry = %s,
                    is_active = %s
                WHERE id = %s
                """, (
                    self.username,
                    self.email,
                    self.password_hash,
                    self.role,
                    self.subscription_type,
                    self.subscription_expiry,
                    self.is_active,
                    self.id
                ))
            else:
                # Insert new user
                cursor.execute("""
                INSERT INTO users (
                    username,
                    email,
                    password_hash,
                    role,
                    subscription_type,
                    subscription_expiry,
                    created_at,
                    is_active
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.username,
                    self.email,
                    self.password_hash,
                    self.role,
                    self.subscription_type,
                    self.subscription_expiry,
                    self.created_at,
                    self.is_active
                ))
                self.id = cursor.lastrowid
            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving user: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

    def delete(self):
        if not self.id:
            return False

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # First, delete all watchlists owned by this user
            cursor.execute("SELECT id FROM watchlists WHERE user_id = %s", (self.id,))
            watchlist_ids = [row['id'] for row in cursor.fetchall()]

            # Delete watchlist items for each watchlist
            if watchlist_ids:
                placeholders = ', '.join(['%s'] * len(watchlist_ids))
                cursor.execute(f"DELETE FROM watchlist_items WHERE watchlist_id IN ({placeholders})", watchlist_ids)

            # Delete watchlists
            cursor.execute("DELETE FROM watchlists WHERE user_id = %s", (self.id,))

            # Finally, delete the user
            cursor.execute("DELETE FROM users WHERE id = %s", (self.id,))

            conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting user: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

# Subscription model
class Subscription:
    def __init__(self, id=None, name=None, price=None, duration_days=None, features=None, created_at=None):
        self.id = id
        self.name = name  # 'free', 'basic', 'premium'
        self.price = price
        self.duration_days = duration_days
        self.features = features  # JSON string of features
        self.created_at = created_at or datetime.datetime.now()

    @staticmethod
    def get_all():
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM subscriptions ORDER BY price")
            subscriptions = []
            for sub_data in cursor.fetchall():
                subscriptions.append(Subscription(
                    id=sub_data['id'],
                    name=sub_data['name'],
                    price=sub_data['price'],
                    duration_days=sub_data['duration_days'],
                    features=sub_data['features'],
                    created_at=sub_data['created_at']
                ))
            return subscriptions
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_by_id(subscription_id):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM subscriptions WHERE id = %s", (subscription_id,))
            sub_data = cursor.fetchone()
            if sub_data:
                return Subscription(
                    id=sub_data['id'],
                    name=sub_data['name'],
                    price=sub_data['price'],
                    duration_days=sub_data['duration_days'],
                    features=sub_data['features'],
                    created_at=sub_data['created_at']
                )
            return None
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_by_name(name):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM subscriptions WHERE name = %s", (name,))
            sub_data = cursor.fetchone()
            if sub_data:
                return Subscription(
                    id=sub_data['id'],
                    name=sub_data['name'],
                    price=sub_data['price'],
                    duration_days=sub_data['duration_days'],
                    features=sub_data['features'],
                    created_at=sub_data['created_at']
                )
            return None
        finally:
            cursor.close()
            conn.close()

    def save(self):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            if self.id:
                # Update existing subscription
                cursor.execute("""
                UPDATE subscriptions SET
                    name = %s,
                    price = %s,
                    duration_days = %s,
                    features = %s
                WHERE id = %s
                """, (
                    self.name,
                    self.price,
                    self.duration_days,
                    self.features,
                    self.id
                ))
            else:
                # Insert new subscription
                cursor.execute("""
                INSERT INTO subscriptions (
                    name,
                    price,
                    duration_days,
                    features,
                    created_at
                ) VALUES (%s, %s, %s, %s, %s)
                """, (
                    self.name,
                    self.price,
                    self.duration_days,
                    self.features,
                    self.created_at
                ))
                self.id = cursor.lastrowid
            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving subscription: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

# Watchlist model
class Watchlist:
    def __init__(self, id=None, user_id=None, name=None, created_at=None):
        self.id = id
        self.user_id = user_id
        self.name = name
        self.created_at = created_at or datetime.datetime.now()
        self.stocks = []  # List of WatchlistItem objects

    @staticmethod
    def get_by_user_id(user_id):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM watchlists WHERE user_id = %s", (user_id,))
            watchlists = []
            for watchlist_data in cursor.fetchall():
                watchlist = Watchlist(
                    id=watchlist_data['id'],
                    user_id=watchlist_data['user_id'],
                    name=watchlist_data['name'],
                    created_at=watchlist_data['created_at']
                )
                # Get watchlist items
                cursor.execute("SELECT * FROM watchlist_items WHERE watchlist_id = %s", (watchlist.id,))
                for item_data in cursor.fetchall():
                    watchlist.stocks.append(WatchlistItem(
                        id=item_data['id'],
                        watchlist_id=item_data['watchlist_id'],
                        instrument_key=item_data['instrument_key'],
                        created_at=item_data['created_at']
                    ))
                watchlists.append(watchlist)
            return watchlists
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_by_id(watchlist_id):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT * FROM watchlists WHERE id = %s", (watchlist_id,))
            watchlist_data = cursor.fetchone()
            if watchlist_data:
                watchlist = Watchlist(
                    id=watchlist_data['id'],
                    user_id=watchlist_data['user_id'],
                    name=watchlist_data['name'],
                    created_at=watchlist_data['created_at']
                )
                # Get watchlist items
                cursor.execute("SELECT * FROM watchlist_items WHERE watchlist_id = %s", (watchlist.id,))
                for item_data in cursor.fetchall():
                    watchlist.stocks.append(WatchlistItem(
                        id=item_data['id'],
                        watchlist_id=item_data['watchlist_id'],
                        instrument_key=item_data['instrument_key'],
                        created_at=item_data['created_at']
                    ))
                return watchlist
            return None
        finally:
            cursor.close()
            conn.close()

    def save(self):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            if self.id:
                # Update existing watchlist
                cursor.execute("""
                UPDATE watchlists SET
                    user_id = %s,
                    name = %s
                WHERE id = %s
                """, (
                    self.user_id,
                    self.name,
                    self.id
                ))
            else:
                # Insert new watchlist
                cursor.execute("""
                INSERT INTO watchlists (
                    user_id,
                    name,
                    created_at
                ) VALUES (%s, %s, %s)
                """, (
                    self.user_id,
                    self.name,
                    self.created_at
                ))
                self.id = cursor.lastrowid
            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving watchlist: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

    def delete(self):
        if not self.id:
            return False

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # Delete all watchlist items first
            cursor.execute("DELETE FROM watchlist_items WHERE watchlist_id = %s", (self.id,))
            # Then delete the watchlist
            cursor.execute("DELETE FROM watchlists WHERE id = %s", (self.id,))
            conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting watchlist: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

# WatchlistItem model
class WatchlistItem:
    def __init__(self, id=None, watchlist_id=None, instrument_key=None, created_at=None):
        self.id = id
        self.watchlist_id = watchlist_id
        self.instrument_key = instrument_key
        self.created_at = created_at or datetime.datetime.now()

    def save(self):
        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            if self.id:
                # Update existing watchlist item
                cursor.execute("""
                UPDATE watchlist_items SET
                    watchlist_id = %s,
                    instrument_key = %s
                WHERE id = %s
                """, (
                    self.watchlist_id,
                    self.instrument_key,
                    self.id
                ))
            else:
                # Insert new watchlist item
                cursor.execute("""
                INSERT INTO watchlist_items (
                    watchlist_id,
                    instrument_key,
                    created_at
                ) VALUES (%s, %s, %s)
                """, (
                    self.watchlist_id,
                    self.instrument_key,
                    self.created_at
                ))
                self.id = cursor.lastrowid
            conn.commit()
            return True
        except Exception as e:
            print(f"Error saving watchlist item: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

    def delete(self):
        if not self.id:
            return False

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("DELETE FROM watchlist_items WHERE id = %s", (self.id,))
            conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting watchlist item: {e}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

# Function to create all required tables
def create_tables():
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Create users table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) NOT NULL DEFAULT 'basic',
            subscription_type VARCHAR(20),
            subscription_expiry DATETIME,
            created_at DATETIME NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            INDEX idx_email (email),
            INDEX idx_username (username)
        )
        """)

        # Create subscriptions table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            duration_days INT NOT NULL,
            features TEXT,
            created_at DATETIME NOT NULL
        )
        """)

        # Create watchlists table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS watchlists (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            created_at DATETIME NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id)
        )
        """)

        # Create watchlist_items table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS watchlist_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            watchlist_id INT NOT NULL,
            instrument_key VARCHAR(100) NOT NULL,
            created_at DATETIME NOT NULL,
            FOREIGN KEY (watchlist_id) REFERENCES watchlists(id) ON DELETE CASCADE,
            INDEX idx_watchlist_id (watchlist_id),
            UNIQUE KEY unique_watchlist_instrument (watchlist_id, instrument_key)
        )
        """)

        # Create default subscription plans
        cursor.execute("SELECT COUNT(*) as count FROM subscriptions")
        count = cursor.fetchone()['count']

        if count == 0:
            # Insert default subscription plans
            cursor.execute("""
            INSERT INTO subscriptions (name, price, duration_days, features, created_at)
            VALUES
                ('free', 0.00, 30, '{"max_watchlists": 1, "max_stocks_per_watchlist": 5, "refresh_rate": 60}', NOW()),
                ('basic', 9.99, 30, '{"max_watchlists": 3, "max_stocks_per_watchlist": 20, "refresh_rate": 30}', NOW()),
                ('premium', 19.99, 30, '{"max_watchlists": 10, "max_stocks_per_watchlist": 100, "refresh_rate": 5}', NOW())
            """)

        # Create admin user if it doesn't exist
        cursor.execute("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")
        count = cursor.fetchone()['count']

        if count == 0:
            # Create admin user with password 'admin123'
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin',
                subscription_type='premium',
                subscription_expiry=datetime.datetime.now() + datetime.timedelta(days=365)
            )
            admin_user.set_password('admin123')

            cursor.execute("""
            INSERT INTO users (username, email, password_hash, role, subscription_type, subscription_expiry, created_at, is_active)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                admin_user.username,
                admin_user.email,
                admin_user.password_hash,
                admin_user.role,
                admin_user.subscription_type,
                admin_user.subscription_expiry,
                admin_user.created_at,
                admin_user.is_active
            ))

        conn.commit()
        print("All tables created successfully!")

    except Exception as e:
        print(f"Error creating tables: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    create_tables()
