import pymysql
import configparser
import os

def get_mysql_config():
    """Get MySQL connection details from config file or create if not exists"""
    config = configparser.ConfigParser()

    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

def create_livestocks_table():
    """Create the livestocks table in MySQL"""
    mysql_config = get_mysql_config()

    try:
        # Connect to MySQL
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Create a cursor
        cursor = conn.cursor()

        # Create livestocks table
        print("Creating livestocks table...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS livestocks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp BIGINT,
            instrument_key VARCHAR(100),
            instrument_token VARCHAR(50),
            last_price FLOAT,
            prev_open FLOAT,
            prev_high FLOAT,
            prev_low FLOAT,
            prev_close FLOAT,
            prev_volume INT,
            prev_ts BIGINT,
            live_open FLOAT,
            live_high FLOAT,
            live_low FLOAT,
            live_close FLOAT,
            live_volume INT,
            live_ts BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        conn.commit()
        print("livestocks table created successfully")

        # Close connection
        conn.close()

        print("\nMySQL livestocks table created successfully!")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    create_livestocks_table()
