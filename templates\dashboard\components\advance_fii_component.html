<!-- Advanced FII Component -->
<div class="tab-pane fade" id="advance-fii-tab-content" role="tabpanel" aria-labelledby="advance-fii-tab">
    <div class="alert alert-info alert-dismissible fade show mb-3" role="alert">
        <strong>Advanced FII Analysis</strong> - Detailed analysis of FII, DII, PRO, and CLIENT trading data with market indicators.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <!-- Loading Indicator -->
    <div id="advance-fii-loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading Advanced FII data...</p>
    </div>

    <!-- Content (hidden initially) -->
    <div id="advance-fii-content" style="display: none;">
        <!-- Filters and Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Filters</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="advance-fii-date-filter" class="form-label">Select Date</label>
                                    <select id="advance-fii-date-filter" class="form-select">
                                        <!-- Dates will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="advance-fii-category-filter" class="form-label">Category</label>
                                    <select id="advance-fii-category-filter" class="form-select">
                                        <option value="all" selected>All Categories</option>
                                        <option value="fii">FII</option>
                                        <option value="dii">DII</option>
                                        <option value="pro">PRO</option>
                                        <option value="client">CLIENT</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="advance-fii-segment-filter" class="form-label">Segment</label>
                                    <select id="advance-fii-segment-filter" class="form-select">
                                        <option value="all" selected>All Segments</option>
                                        <option value="cash">Cash</option>
                                        <option value="future">Future</option>
                                        <option value="option">Option</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="advance-fii-view-filter" class="form-label">View</label>
                                    <select id="advance-fii-view-filter" class="form-select">
                                        <option value="all" selected>All Views</option>
                                        <option value="bullish">Bullish</option>
                                        <option value="bearish">Bearish</option>
                                        <option value="neutral">Neutral</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Market Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>NIFTY:</span>
                                    <span id="advance-fii-nifty" class="fw-bold">-</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>NIFTY Change:</span>
                                    <span id="advance-fii-nifty-change" class="fw-bold">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>BANKNIFTY:</span>
                                    <span id="advance-fii-banknifty" class="fw-bold">-</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>BANKNIFTY Change:</span>
                                    <span id="advance-fii-banknifty-change" class="fw-bold">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <span>Date:</span>
                            <span id="advance-fii-date" class="fw-bold">-</span>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <span>Next Market Open:</span>
                            <span id="advance-fii-next-market-open" class="fw-bold">-</span>
                        </div>
                        <div class="text-end mt-3">
                            <small id="advance-fii-last-updated" class="text-muted">Last updated: -</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Market Section -->
        <div class="row mb-4" id="cash-section">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Cash Market</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Buy (Cr)</th>
                                        <th>Sell (Cr)</th>
                                        <th>Net (Cr)</th>
                                        <th>Action</th>
                                        <th>View</th>
                                        <th>Strength</th>
                                    </tr>
                                </thead>
                                <tbody id="advance-fii-cash-table">
                                    <!-- Data will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Futures Section -->
        <div class="row mb-4" id="futures-section">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Futures</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="futures-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="futures-index-tab" data-bs-toggle="tab" data-bs-target="#futures-index" type="button" role="tab">Index Futures</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="futures-stock-tab" data-bs-toggle="tab" data-bs-target="#futures-stock" type="button" role="tab">Stock Futures</button>
                            </li>
                        </ul>
                        <div class="tab-content mt-3" id="futures-tabs-content">
                            <div class="tab-pane fade show active" id="futures-index" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Net OI</th>
                                                <th>Outstanding OI</th>
                                                <th>Action</th>
                                                <th>View</th>
                                                <th>Strength</th>
                                                <th>NIFTY</th>
                                                <th>BANKNIFTY</th>
                                                <th>FINNIFTY</th>
                                            </tr>
                                        </thead>
                                        <tbody id="advance-fii-futures-index-table">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="futures-stock" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Net OI</th>
                                                <th>Outstanding OI</th>
                                                <th>Action</th>
                                                <th>View</th>
                                                <th>Strength</th>
                                            </tr>
                                        </thead>
                                        <tbody id="advance-fii-futures-stock-table">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Individual Index Futures Section -->
        {% include 'dashboard/components/index_futures_component.html' %}

        <!-- Amount-wise Futures Section -->
        {% include 'dashboard/components/amount_futures_component.html' %}

        <!-- Options Section -->
        <div class="row mb-4" id="options-section">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Options</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="options-tabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="options-call-tab" data-bs-toggle="tab" data-bs-target="#options-call" type="button" role="tab">Call Options</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="options-put-tab" data-bs-toggle="tab" data-bs-target="#options-put" type="button" role="tab">Put Options</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="options-overall-tab" data-bs-toggle="tab" data-bs-target="#options-overall" type="button" role="tab">Overall</button>
                            </li>
                        </ul>
                        <div class="tab-content mt-3" id="options-tabs-content">
                            <div class="tab-pane fade show active" id="options-call" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Long OI</th>
                                                <th>Long Change</th>
                                                <th>Short OI</th>
                                                <th>Short Change</th>
                                                <th>Net OI</th>
                                                <th>Net Change</th>
                                                <th>Action</th>
                                                <th>View</th>
                                                <th>Strength</th>
                                            </tr>
                                        </thead>
                                        <tbody id="advance-fii-options-call-table">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="options-put" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Long OI</th>
                                                <th>Long Change</th>
                                                <th>Short OI</th>
                                                <th>Short Change</th>
                                                <th>Net OI</th>
                                                <th>Net Change</th>
                                                <th>Action</th>
                                                <th>View</th>
                                                <th>Strength</th>
                                            </tr>
                                        </thead>
                                        <tbody id="advance-fii-options-put-table">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="options-overall" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Overall Net OI</th>
                                                <th>Overall Net Change</th>
                                                <th>Action</th>
                                                <th>View</th>
                                                <th>Strength</th>
                                                <th>View Summary</th>
                                            </tr>
                                        </thead>
                                        <tbody id="advance-fii-options-overall-table">
                                            <!-- Data will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
