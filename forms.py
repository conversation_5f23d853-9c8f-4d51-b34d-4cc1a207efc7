from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, BooleanField, SubmitField, SelectField, DateField, TextAreaField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from models import User
import datetime

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    username = String<PERSON>ield('Username', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField(
        'Repeat Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Register')

    def validate_username(self, username):
        user = User.get_by_username(username.data)
        if user is not None:
            raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        user = User.get_by_email(email.data)
        if user is not None:
            raise ValidationError('Please use a different email address.')

class SubscriptionForm(FlaskForm):
    plan = SelectField('Subscription Plan', choices=[
        ('free', 'Free - $0/month'),
        ('basic', 'Basic - $9.99/month'),
        ('premium', 'Premium - $19.99/month')
    ], validators=[DataRequired()])
    submit = SubmitField('Subscribe')

class WatchlistForm(FlaskForm):
    name = StringField('Watchlist Name', validators=[DataRequired(), Length(min=1, max=100)])
    submit = SubmitField('Create Watchlist')

class AddStockForm(FlaskForm):
    instrument_key = StringField('Stock Symbol', validators=[DataRequired()])
    submit = SubmitField('Add to Watchlist')

class UserCreateForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Repeat Password', validators=[DataRequired(), EqualTo('password')])
    role = SelectField('Role', choices=[
        ('basic', 'Basic User'),
        ('admin', 'Administrator')
    ], validators=[DataRequired()])
    subscription_type = SelectField('Subscription Type', choices=[
        ('free', 'Free'),
        ('basic', 'Basic'),
        ('premium', 'Premium')
    ], validators=[DataRequired()])
    subscription_expiry = DateField('Subscription Expiry', format='%Y-%m-%d',
                                   default=datetime.datetime.now() + datetime.timedelta(days=30),
                                   validators=[DataRequired()])
    is_active = BooleanField('Active Account', default=True)
    submit = SubmitField('Create User')

    def validate_username(self, username):
        user = User.get_by_username(username.data)
        if user is not None:
            raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        user = User.get_by_email(email.data)
        if user is not None:
            raise ValidationError('Please use a different email address.')

class UserEditForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('New Password', validators=[Optional(), Length(min=6)])
    password2 = PasswordField('Repeat New Password', validators=[EqualTo('password')])
    role = SelectField('Role', choices=[
        ('basic', 'Basic User'),
        ('admin', 'Administrator')
    ], validators=[DataRequired()])
    subscription_type = SelectField('Subscription Type', choices=[
        ('free', 'Free'),
        ('basic', 'Basic'),
        ('premium', 'Premium')
    ], validators=[DataRequired()])
    subscription_expiry = DateField('Subscription Expiry', format='%Y-%m-%d', validators=[DataRequired()])
    is_active = BooleanField('Active Account', default=True)
    submit = SubmitField('Update User')

    def __init__(self, original_username, original_email, *args, **kwargs):
        super(UserEditForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email

    def validate_username(self, username):
        if username.data != self.original_username:
            user = User.get_by_username(username.data)
            if user is not None:
                raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.get_by_email(email.data)
            if user is not None:
                raise ValidationError('Please use a different email address.')

class SubscriptionEditForm(FlaskForm):
    name = StringField('Name', validators=[DataRequired(), Length(min=2, max=50)])
    price = StringField('Price', validators=[DataRequired()])
    duration_days = StringField('Duration (days)', validators=[DataRequired()])
    features = TextAreaField('Features (JSON)', validators=[DataRequired()])
    submit = SubmitField('Update Subscription')
