# Database Migration Tool

This tool helps you migrate all tables and data from the `option_Chain_data` database to the `option_chain_db` database.

## Prerequisites

- MySQL server installed and running
- Access to both source and target databases
- Sufficient disk space for the migration

## Migration Options

You have several options to perform the migration:

### Option 1: Using the Python Script (Recommended)

The Python script provides a user-friendly interface and handles the migration in batches to prevent memory issues with large tables.

1. Make sure you have Python installed with the `pymysql` package:
   ```
   pip install pymysql
   ```

2. Run the Python script:
   ```
   python migrate_database.py
   ```

3. Follow the prompts to confirm actions for existing tables.

### Option 2: Using the Batch File (Windows)

1. Open Command Prompt
2. Navigate to the directory containing the scripts
3. Run the batch file:
   ```
   migrate_database.bat
   ```

4. Follow the prompts to confirm actions for existing tables.

### Option 3: Using the Shell Script (Linux/Mac)

1. Make the script executable:
   ```
   chmod +x migrate_database.sh
   ```

2. Run the shell script:
   ```
   ./migrate_database.sh
   ```

3. Follow the prompts to confirm actions for existing tables.

### Option 4: Using the SQL Script

1. Log in to MySQL:
   ```
   mysql -u root -p
   ```

2. Run the SQL script:
   ```
   source migrate_database.sql
   ```

## Configuration

All scripts use the following default configuration:
- Host: localhost
- User: root
- Password: vinayak123
- Source Database: option_Chain_data
- Target Database: option_chain_db

If you need to change these settings:
- For the Python script: Edit the `get_mysql_config()` function
- For the Batch file: Edit the variables at the top of the file
- For the Shell script: Edit the variables at the top of the file
- For the SQL script: Edit the `@source_db` and `@target_db` variables

## Troubleshooting

### Common Issues:

1. **Access Denied**: Make sure you have the correct username and password with sufficient privileges.

2. **Database Not Found**: Ensure both source and target databases exist.

3. **Table Already Exists**: The scripts will prompt you to decide whether to drop and recreate existing tables.

4. **Memory Issues**: For very large tables, the Python script uses batch processing to prevent memory issues.

### Logs:

All scripts provide detailed output of the migration process. If you encounter issues, review the output for error messages.

## After Migration

After the migration is complete:
1. Verify that all tables were created in the target database
2. Verify that the row counts match between source and target tables
3. Test your application with the new database

## Support

If you encounter any issues, please contact the development team for assistance.
