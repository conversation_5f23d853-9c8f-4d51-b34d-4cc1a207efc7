import requests
import json
import time
import os
import pymysql
import configparser
import datetime
import threading
from datetime import datetime

# Upstox API credentials
client_id = '137f6db0-d790-4774-96af-52a375363950'
secret_key = 'l0lwdy7zqq'
redirect_uri = 'https://api.upstox.com/v2/login'

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_db'
        }

# Function to get database connection
def get_db_connection():
    mysql_config = get_mysql_config()
    return pymysql.connect(
        host=mysql_config['host'],
        port=int(mysql_config['port']),
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=mysql_config['database']
    )

# Function to ensure the livestocks table exists
def ensure_livestocks_table_exists():
    """Check if the livestocks table exists and create it if it doesn't"""
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if table exists
        cursor.execute("SHOW TABLES LIKE 'livestocks'")
        table_exists = cursor.fetchone()

        if not table_exists:
            print("Creating livestocks table...")
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS livestocks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME,
                instrument_key VARCHAR(100),
                instrument_token VARCHAR(50),
                last_price FLOAT,
                prev_open FLOAT,
                prev_high FLOAT,
                prev_low FLOAT,
                prev_close FLOAT,
                prev_volume INT,
                prev_ts DATETIME,
                live_open FLOAT,
                live_high FLOAT,
                live_low FLOAT,
                live_close FLOAT,
                live_volume INT,
                live_ts DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE INDEX idx_instrument_key (instrument_key),
                INDEX idx_id (id)
            )
            """)
            conn.commit()
            print("livestocks table created successfully")
        else:
            # Check if we need to modify the existing table
            try:
                # Check if the unique index exists
                cursor.execute("SHOW INDEX FROM livestocks WHERE Key_name = 'idx_instrument_key'")
                index_exists = cursor.fetchone()

                if not index_exists:
                    print("Adding unique index on instrument_key...")
                    cursor.execute("ALTER TABLE livestocks ADD UNIQUE INDEX idx_instrument_key (instrument_key)")
                    conn.commit()

                # Check if timestamp columns are DATETIME
                cursor.execute("DESCRIBE livestocks")
                columns = cursor.fetchall()

                timestamp_columns = ['timestamp', 'prev_ts', 'live_ts']
                for column in columns:
                    column_name = column[0]
                    column_type = column[1]

                    if column_name in timestamp_columns and 'datetime' not in column_type.lower():
                        print(f"Converting {column_name} to DATETIME...")
                        cursor.execute(f"ALTER TABLE livestocks MODIFY COLUMN {column_name} DATETIME")
                        conn.commit()

                # Check if we need to add an index on id
                cursor.execute("SHOW INDEX FROM livestocks WHERE Key_name = 'idx_id'")
                id_index_exists = cursor.fetchone()

                if not id_index_exists:
                    print("Adding index on id...")
                    cursor.execute("ALTER TABLE livestocks ADD INDEX idx_id (id)")
                    conn.commit()

                print("Table structure updated successfully")
            except Exception as e:
                print(f"Error updating table structure: {e}")
                # If there was an error, drop and recreate the table
                cursor.execute("DROP TABLE livestocks")
                conn.commit()

                cursor.execute("""
                CREATE TABLE IF NOT EXISTS livestocks (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME,
                    instrument_key VARCHAR(100),
                    instrument_token VARCHAR(50),
                    last_price FLOAT,
                    prev_open FLOAT,
                    prev_high FLOAT,
                    prev_low FLOAT,
                    prev_close FLOAT,
                    prev_volume INT,
                    prev_ts DATETIME,
                    live_open FLOAT,
                    live_high FLOAT,
                    live_low FLOAT,
                    live_close FLOAT,
                    live_volume INT,
                    live_ts DATETIME,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE INDEX idx_instrument_key (instrument_key),
                    INDEX idx_id (id)
                )
                """)
                conn.commit()
                print("livestocks table recreated successfully")

        print("Table structure is ready")

    except Exception as e:
        print(f"Error with database: {e}")
    finally:
        cursor.close()
        conn.close()

# Function to read ind_nifty500list.csv and extract symbols
def get_nse_symbols():
    try:
        import csv

        # Path to the Nifty 500 list CSV file
        csv_path = 'NSE/ind_nifty500list.csv'

        # Read the CSV file
        symbols = []
        with open(csv_path, 'r') as f:
            csv_reader = csv.DictReader(f)
            for row in csv_reader:
                # Use ISIN Code as instrument_key
                if 'ISIN Code' in row and row['ISIN Code']:
                    symbols.append('NSE_EQ|'+row['ISIN Code'])

        # Get the first 500 symbols (or all if less than 500)
        symbols = symbols[:500]

        print(f"Found {len(symbols)} NSE symbols from Nifty 500 list")
        print(f"Using {len(symbols)} symbols for data collection")

        return symbols
    except Exception as e:
        print(f"Error reading Nifty 500 list: {e}")
        print(f"File path: NSE/ind_nifty500list.csv")
        # Fallback to empty list
        return []

# Function to get or refresh access token
def get_access_token():
    token_file = 'upstox_token.json'

    # Check if token file exists and is still valid
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as f:
                token_data = json.load(f)

            # Check if token is less than 1 day old
            if 'timestamp' in token_data:
                token_time = token_data['timestamp']
                current_time = time.time()

                # If token is less than 1 day old (86400 seconds), use it
                if current_time - token_time < 86400:
                    print("Using existing access token")
                    return token_data['access_token']
        except Exception as e:
            print(f"Error reading token file: {e}")

    # If we get here, we need a new token
    print("Getting new access token...")
    url = f'https://api.upstox.com/v2/login/authorization/dialog?response_type=code&client_id={client_id}&redirect_uri={redirect_uri}'
    print(f"Please visit this URL to authorize: {url}")
    code = input('Enter the code from the URL: ')

    url = 'https://api.upstox.com/v2/login/authorization/token'
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
    }

    data = {
        'code': code,
        'client_id': client_id,
        'client_secret': secret_key,
        'redirect_uri': redirect_uri,
        'grant_type': 'authorization_code',
    }

    response = requests.post(url, headers=headers, data=data)

    if response.status_code == 200:
        response_data = response.json()
        access_token = response_data['access_token']

        # Save token with timestamp
        token_data = {
            'access_token': access_token,
            'timestamp': time.time()
        }

        with open(token_file, 'w') as f:
            json.dump(token_data, f)

        return access_token
    else:
        print(f"Error getting access token: {response.status_code}")
        print(response.json())
        return None

# Function to fetch stock data from Upstox API
def fetch_stock_data(symbols, access_token):
    url = 'https://api.upstox.com/v3/market-quote/ohlc'
    headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + access_token,
    }

    # Join symbols with commas for the API request
    # Split into chunks of 50 symbols each to avoid request size limits
    chunk_size = 500
    all_data = {}
    total_symbols = len(symbols)
    processed_symbols = 0

    print(f"Fetching data for {total_symbols} symbols in chunks of {chunk_size}...")

    for i in range(0, total_symbols, chunk_size):
        chunk = symbols[i:i+chunk_size]
        chunk_size_actual = len(chunk)
        processed_symbols += chunk_size_actual

        print(f"Processing chunk {i//chunk_size + 1}: symbols {i+1}-{i+chunk_size_actual} of {total_symbols}")

        instrument_keys = ",".join(chunk)

        data = {
            "instrument_key": instrument_keys,
            "interval": "I5"  # Use 1-minute interval for more frequent data
        }

        try:
            response = requests.get(url, headers=headers, params=data)

            if response.status_code == 200:
                response_data = response.json()
                if response_data.get('status') == 'success':
                    chunk_data = response_data.get('data', {})
                    if chunk_data:
                        all_data.update(chunk_data)
                        print(f"  Received data for {len(chunk_data)} symbols in this chunk")
                    else:
                        print(f"  Warning: No data received for this chunk")
                else:
                    print(f"  API returned error: {response_data}")
            else:
                print(f"  API request failed: {response.status_code}")
                try:
                    print(f"  Error details: {response.json()}")
                except:
                    print(f"  Could not parse error response")
        except Exception as e:
            print(f"  Error fetching data: {e}")

    print(f"Completed fetching data. Received data for {len(all_data)} out of {total_symbols} symbols")
    return all_data

# Function to convert Unix timestamp to datetime
def unix_to_datetime(unix_ts):
    if not unix_ts:
        return None
    try:
        return datetime.fromtimestamp(unix_ts / 1000)  # Convert milliseconds to seconds
    except Exception as e:
        print(f"Error converting timestamp {unix_ts}: {e}")
        return None

# Function to store stock data in MySQL
def store_stock_data(data):
    if not data:
        print("No data to store")
        return

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        current_timestamp = datetime.now()
        stored_count = 0

        # Prepare data for bulk insert/update
        bulk_data = []

        for instrument_key, stock_data in data.items():
            if stock_data is None:
                print(f"Warning: No data for {instrument_key}, skipping")
                continue

            try:
                # Extract data
                instrument_token = stock_data.get('instrument_token', '')
                last_price = stock_data.get('last_price', 0)

                prev_ohlc = stock_data.get('prev_ohlc', {}) or {}  # Ensure it's not None
                prev_open = prev_ohlc.get('open', 0)
                prev_high = prev_ohlc.get('high', 0)
                prev_low = prev_ohlc.get('low', 0)
                prev_close = prev_ohlc.get('close', 0)
                prev_volume = prev_ohlc.get('volume', 0)
                prev_ts = unix_to_datetime(prev_ohlc.get('ts', 0))

                live_ohlc = stock_data.get('live_ohlc', {}) or {}  # Ensure it's not None
                live_open = live_ohlc.get('open', 0)
                live_high = live_ohlc.get('high', 0)
                live_low = live_ohlc.get('low', 0)
                live_close = live_ohlc.get('close', 0)
                live_volume = live_ohlc.get('volume', 0)
                live_ts = unix_to_datetime(live_ohlc.get('ts', 0))

                # Add to bulk data
                bulk_data.append((
                    current_timestamp, instrument_key, instrument_token, last_price,
                    prev_open, prev_high, prev_low, prev_close, prev_volume, prev_ts,
                    live_open, live_high, live_low, live_close, live_volume, live_ts
                ))
                stored_count += 1
            except Exception as e:
                print(f"Error processing {instrument_key}: {e}")

        if bulk_data:
            # Use INSERT ... ON DUPLICATE KEY UPDATE for updating existing records
            query = """
            INSERT INTO livestocks (
                timestamp, instrument_key, instrument_token, last_price,
                prev_open, prev_high, prev_low, prev_close, prev_volume, prev_ts,
                live_open, live_high, live_low, live_close, live_volume, live_ts
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            ON DUPLICATE KEY UPDATE
                timestamp = VALUES(timestamp),
                instrument_token = VALUES(instrument_token),
                last_price = VALUES(last_price),
                prev_open = VALUES(prev_open),
                prev_high = VALUES(prev_high),
                prev_low = VALUES(prev_low),
                prev_close = VALUES(prev_close),
                prev_volume = VALUES(prev_volume),
                prev_ts = VALUES(prev_ts),
                live_open = VALUES(live_open),
                live_high = VALUES(live_high),
                live_low = VALUES(live_low),
                live_close = VALUES(live_close),
                live_volume = VALUES(live_volume),
                live_ts = VALUES(live_ts)
            """

            # Execute bulk insert/update
            cursor.executemany(query, bulk_data)
            conn.commit()

            print(f"Updated data for {stored_count} stocks at {current_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("No data to update")

    except Exception as e:
        print(f"Error storing data: {e}")
    finally:
        cursor.close()
        conn.close()

# Main function
def main():
    # Ensure the livestocks table exists
    ensure_livestocks_table_exists()

    # Get access token
    access_token = get_access_token()
    if not access_token:
        print("Failed to get access token. Exiting.")
        return

    # Get NSE symbols
    symbols = get_nse_symbols()
    if not symbols:
        print("Failed to get NSE symbols. Exiting.")
        return

    print(f"Starting data collection for {len(symbols)} symbols...")

    # Main loop to fetch and store data every second
    try:
        while True:
            start_time = time.time()

            # Fetch stock data
            stock_data = fetch_stock_data(symbols, access_token)

            # Store stock data
            store_stock_data(stock_data)

            # Calculate time taken and sleep if needed
            elapsed = time.time() - start_time
            if elapsed < 1:
                sleep_time = 1 - elapsed
                print(f"Sleeping for {sleep_time:.2f} seconds to maintain 1-second refresh rate")
                time.sleep(sleep_time)
            else:
                print(f"Warning: Data processing took {elapsed:.2f} seconds, exceeding the 1-second refresh target")

    except KeyboardInterrupt:
        print("Data collection stopped by user")
    except Exception as e:
        print(f"Error in main loop: {e}")

if __name__ == "__main__":
    main()
