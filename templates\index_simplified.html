{% extends "base.html" %}

{% block title %}Stock Market Dashboard{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin-bottom: 40px;
        border-radius: 10px;
    }
    .feature-card {
        transition: transform 0.3s ease;
        margin-bottom: 30px;
        height: 100%;
    }
    .feature-card:hover {
        transform: translateY(-5px);
    }
    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 20px;
        color: #007bff;
    }
    .cta-section {
        background-color: #e9ecef;
        padding: 40px 0;
        border-radius: 10px;
        margin-top: 30px;
    }
    .subscription-card {
        transition: transform 0.3s ease;
        height: 100%;
    }
    .subscription-card:hover {
        transform: translateY(-5px);
    }
    .subscription-card.popular {
        border-color: #007bff;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 123, 0.15);
    }
    .popular-badge {
        position: absolute;
        top: -10px;
        right: 10px;
        z-index: 1;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Stock Market Analysis Platform</h1>
                <p class="lead mb-4">Get real-time data, advanced analytics, and powerful tools to make informed trading decisions.</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg px-4 me-md-2">Go to Dashboard</a>
                    <a href="{{ url_for('option_data') }}" class="btn btn-outline-primary btn-lg px-4">Option Chain Analysis</a>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg px-4 me-md-2">Login</a>
                    <a href="{{ url_for('register') }}" class="btn btn-outline-primary btn-lg px-4">Register</a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <img src="https://via.placeholder.com/600x400?text=Stock+Market+Dashboard" class="img-fluid rounded" alt="Dashboard Preview">
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container mb-5">
    <h2 class="text-center mb-5">Key Features</h2>
    <div class="row">
        <div class="col-md-4">
            <div class="card feature-card">
                <div class="card-body text-center">
                    <div class="feature-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h5 class="card-title">Live Stock Dashboard</h5>
                    <p class="card-text">Track your favorite stocks in real-time with customizable watchlists and detailed analytics.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card feature-card">
                <div class="card-body text-center">
                    <div class="feature-icon">
                        <i class="bi bi-bar-chart"></i>
                    </div>
                    <h5 class="card-title">Option Chain Analysis</h5>
                    <p class="card-text">Analyze option chains with advanced tools for Open Interest, historical data, and more.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card feature-card">
                <div class="card-body text-center">
                    <div class="feature-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <h5 class="card-title">Historical Data</h5>
                    <p class="card-text">Access historical market data to analyze trends and make informed decisions.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subscription Plans Section -->
<div class="container mb-5">
    <h2 class="text-center mb-5">Subscription Plans</h2>
    <div class="row">
        <div class="col-md-4">
            <div class="card subscription-card">
                <div class="card-header text-center">
                    <h5 class="my-0 fw-normal">Free</h5>
                </div>
                <div class="card-body">
                    <h1 class="card-title text-center">$0<small class="text-muted fw-light">/mo</small></h1>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>1 Watchlist</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>5 Stocks per Watchlist</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>60-second Refresh Rate</li>
                        <li class="mb-2"><i class="bi bi-x text-danger me-2"></i>Limited Historical Data</li>
                    </ul>
                    <div class="d-grid">
                        {% if not current_user.is_authenticated %}
                        <a href="{{ url_for('register') }}" class="btn btn-outline-primary">Sign Up Free</a>
                        {% elif current_user.subscription_type == 'free' %}
                        <button class="btn btn-outline-secondary" disabled>Current Plan</button>
                        {% else %}
                        <a href="{{ url_for('subscriptions') }}" class="btn btn-outline-primary">Switch to Free</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card subscription-card popular">
                <div class="popular-badge">
                    <span class="badge bg-primary">Popular</span>
                </div>
                <div class="card-header text-center">
                    <h5 class="my-0 fw-normal">Basic</h5>
                </div>
                <div class="card-body">
                    <h1 class="card-title text-center">$9.99<small class="text-muted fw-light">/mo</small></h1>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>3 Watchlists</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>20 Stocks per Watchlist</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>30-second Refresh Rate</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>Full Historical Data</li>
                    </ul>
                    <div class="d-grid">
                        {% if not current_user.is_authenticated %}
                        <a href="{{ url_for('register') }}" class="btn btn-primary">Sign Up</a>
                        {% elif current_user.subscription_type == 'basic' %}
                        <button class="btn btn-outline-secondary" disabled>Current Plan</button>
                        {% else %}
                        <a href="{{ url_for('subscriptions') }}" class="btn btn-primary">Upgrade to Basic</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card subscription-card">
                <div class="card-header text-center">
                    <h5 class="my-0 fw-normal">Premium</h5>
                </div>
                <div class="card-body">
                    <h1 class="card-title text-center">$19.99<small class="text-muted fw-light">/mo</small></h1>
                    <ul class="list-unstyled mt-3 mb-4">
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>10 Watchlists</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>100 Stocks per Watchlist</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>5-second Refresh Rate</li>
                        <li class="mb-2"><i class="bi bi-check text-success me-2"></i>Advanced Analytics</li>
                    </ul>
                    <div class="d-grid">
                        {% if not current_user.is_authenticated %}
                        <a href="{{ url_for('register') }}" class="btn btn-outline-primary">Sign Up</a>
                        {% elif current_user.subscription_type == 'premium' %}
                        <button class="btn btn-outline-secondary" disabled>Current Plan</button>
                        {% else %}
                        <a href="{{ url_for('subscriptions') }}" class="btn btn-outline-primary">Upgrade to Premium</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 text-center">
                <h2 class="mb-4">Ready to start trading smarter?</h2>
                <p class="lead mb-4">Join thousands of traders who use our platform to make informed decisions.</p>
                {% if not current_user.is_authenticated %}
                <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg">Get Started Today</a>
                {% else %}
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">Go to Dashboard</a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
