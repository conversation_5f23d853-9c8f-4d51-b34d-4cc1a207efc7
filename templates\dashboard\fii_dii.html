{% extends "base.html" %}

{% block title %}FII/DII Data{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">FII/DII Data Analysis</h4>
            </div>
            <div class="card-body">
                <p class="lead">View and analyze FII (Foreign Institutional Investors) and DII (Domestic Institutional Investors) trading data.</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">FII/DII Trading Activity</h5>
                    <div class="form-group mb-0">
                        <select id="days-filter" class="form-select">
                            <option value="7">Last 7 days</option>
                            <option value="15">Last 15 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="60">Last 60 days</option>
                            <option value="90">Last 90 days</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="loading-indicator" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading FII/DII data...</p>
                </div>

                <div id="fii-dii-content" style="display: none;">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">FII Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Total Buy (Cr)</h6>
                                                <h3 id="fii-total-buy">0</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Total Sell (Cr)</h6>
                                                <h3 id="fii-total-sell">0</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Net (Cr)</h6>
                                                <h3 id="fii-total-net">0</h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Buy (Cr)</h6>
                                                <h5 id="fii-avg-buy">0</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Sell (Cr)</h6>
                                                <h5 id="fii-avg-sell">0</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Net (Cr)</h6>
                                                <h5 id="fii-avg-net">0</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">DII Summary</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Total Buy (Cr)</h6>
                                                <h3 id="dii-total-buy">0</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Total Sell (Cr)</h6>
                                                <h3 id="dii-total-sell">0</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center mb-3">
                                                <h6>Net (Cr)</h6>
                                                <h3 id="dii-total-net">0</h3>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Buy (Cr)</h6>
                                                <h5 id="dii-avg-buy">0</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Sell (Cr)</h6>
                                                <h5 id="dii-avg-sell">0</h5>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h6>Avg Net (Cr)</h6>
                                                <h5 id="dii-avg-net">0</h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Net Investment Trend</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="net-investment-chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">FII Buy vs Sell</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="fii-buy-sell-chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">DII Buy vs Sell</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="dii-buy-sell-chart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Detailed Data</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>FII Buy (Cr)</th>
                                                    <th>FII Sell (Cr)</th>
                                                    <th>FII Net (Cr)</th>
                                                    <th>DII Buy (Cr)</th>
                                                    <th>DII Sell (Cr)</th>
                                                    <th>DII Net (Cr)</th>
                                                </tr>
                                            </thead>
                                            <tbody id="fii-dii-table-body">
                                                <!-- Data will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    $(document).ready(function() {
        // Chart objects
        let netInvestmentChart = null;
        let fiiBuySellChart = null;
        let diiBuySellChart = null;

        // Refresh control variables
        let refreshInterval = null;
        let isTabActive = false; // Track if this tab is currently active

        // Function to format numbers with commas and 2 decimal places
        function formatNumber(num) {
            return new Intl.NumberFormat('en-IN', {
                maximumFractionDigits: 2,
                minimumFractionDigits: 2
            }).format(num);
        }

        // Function to set color based on value
        function getValueColor(value) {
            return value >= 0 ? 'text-success' : 'text-danger';
        }

        // Function to smoothly update text with animation
        function smoothUpdateText(selector, newValue) {
            const element = $(selector);
            const oldValue = element.text();

            if (oldValue !== newValue) {
                element.fadeOut(100, function() {
                    $(this).text(newValue).fadeIn(100);
                });
            }
        }

        // Function to load FII/DII data
        function loadFiiDiiData(days = 30) {
            // Only show loading indicator on first load
            if ($('#fii-dii-content').css('display') === 'none') {
                $('#loading-indicator').show();
            }

            $.ajax({
                url: '/api/fii-dii',
                method: 'GET',
                data: { days: days },
                success: function(response) {
                    // Update summary cards with smooth transitions
                    smoothUpdateText('#fii-total-buy', formatNumber(response.fii_summary.total_buy));
                    smoothUpdateText('#fii-total-sell', formatNumber(response.fii_summary.total_sell));
                    smoothUpdateText('#fii-total-net', formatNumber(response.fii_summary.total_net));
                    $('#fii-total-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.fii_summary.total_net));

                    smoothUpdateText('#fii-avg-buy', formatNumber(response.fii_summary.avg_buy));
                    smoothUpdateText('#fii-avg-sell', formatNumber(response.fii_summary.avg_sell));
                    smoothUpdateText('#fii-avg-net', formatNumber(response.fii_summary.avg_net));
                    $('#fii-avg-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.fii_summary.avg_net));

                    smoothUpdateText('#dii-total-buy', formatNumber(response.dii_summary.total_buy));
                    smoothUpdateText('#dii-total-sell', formatNumber(response.dii_summary.total_sell));
                    smoothUpdateText('#dii-total-net', formatNumber(response.dii_summary.total_net));
                    $('#dii-total-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.dii_summary.total_net));

                    smoothUpdateText('#dii-avg-buy', formatNumber(response.dii_summary.avg_buy));
                    smoothUpdateText('#dii-avg-sell', formatNumber(response.dii_summary.avg_sell));
                    smoothUpdateText('#dii-avg-net', formatNumber(response.dii_summary.avg_net));
                    $('#dii-avg-net').removeClass('text-success text-danger')
                        .addClass(getValueColor(response.dii_summary.avg_net));

                    // Prepare data for charts
                    const dates = response.dates.reverse(); // Reverse to show oldest to newest

                    // Create a map of FII data by date
                    const fiiDataMap = {};
                    response.fii_data.forEach(item => {
                        fiiDataMap[item.date] = item;
                    });

                    // Create a map of DII data by date
                    const diiDataMap = {};
                    response.dii_data.forEach(item => {
                        diiDataMap[item.date] = item;
                    });

                    // Prepare data for charts
                    const fiiNetValues = [];
                    const diiNetValues = [];
                    const fiiBuyValues = [];
                    const fiiSellValues = [];
                    const diiBuyValues = [];
                    const diiSellValues = [];

                    dates.forEach(date => {
                        const fiiData = fiiDataMap[date] || { net_value: 0, buy_value: 0, sell_value: 0 };
                        const diiData = diiDataMap[date] || { net_value: 0, buy_value: 0, sell_value: 0 };

                        fiiNetValues.push(fiiData.net_value);
                        diiNetValues.push(diiData.net_value);
                        fiiBuyValues.push(fiiData.buy_value);
                        fiiSellValues.push(fiiData.sell_value);
                        diiBuyValues.push(diiData.buy_value);
                        diiSellValues.push(diiData.sell_value);
                    });

                    // Update charts
                    updateNetInvestmentChart(dates, fiiNetValues, diiNetValues);
                    updateFiiBuySellChart(dates, fiiBuyValues, fiiSellValues);
                    updateDiiBuySellChart(dates, diiBuyValues, diiSellValues);

                    // Update table
                    updateDataTable(dates, fiiDataMap, diiDataMap);

                    // Hide loading indicator and show content
                    $('#loading-indicator').hide();
                    $('#fii-dii-content').show();

                    // Update last refreshed time
                    const lastUpdated = new Date().toLocaleTimeString();
                    if ($('#last-updated').length === 0) {
                        // Add last updated element if it doesn't exist
                        $('.card-header:first').append(`<small id="last-updated" class="text-muted ms-2">Last updated: ${lastUpdated}</small>`);
                    } else {
                        $('#last-updated').text(`Last updated: ${lastUpdated}`);
                    }
                },
                error: function(error) {
                    console.error('Error fetching FII/DII data:', error);
                    $('#loading-indicator').hide();
                    // Only show alert if this is the first load
                    if ($('#fii-dii-content').css('display') === 'none') {
                        alert('Error loading FII/DII data. Please try again later.');
                    }
                }
            });
        }

        // Function to update Net Investment Chart
        function updateNetInvestmentChart(dates, fiiNetValues, diiNetValues) {
            const ctx = document.getElementById('net-investment-chart').getContext('2d');

            if (netInvestmentChart) {
                netInvestmentChart.destroy();
            }

            netInvestmentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'FII Net Investment',
                            data: fiiNetValues,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderWidth: 2,
                            tension: 0.1,
                            fill: true
                        },
                        {
                            label: 'DII Net Investment',
                            data: diiNetValues,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderWidth: 2,
                            tension: 0.1,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'FII vs DII Net Investment Trend (in Cr)'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += formatNumber(context.raw);
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Amount (Cr)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    }
                }
            });
        }

        // Function to update FII Buy vs Sell Chart
        function updateFiiBuySellChart(dates, buyValues, sellValues) {
            const ctx = document.getElementById('fii-buy-sell-chart').getContext('2d');

            if (fiiBuySellChart) {
                fiiBuySellChart.destroy();
            }

            fiiBuySellChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'Buy',
                            data: buyValues,
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Sell',
                            data: sellValues,
                            backgroundColor: 'rgba(255, 99, 132, 0.7)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'FII Buy vs Sell (in Cr)'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += formatNumber(context.raw);
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Amount (Cr)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    }
                }
            });
        }

        // Function to update DII Buy vs Sell Chart
        function updateDiiBuySellChart(dates, buyValues, sellValues) {
            const ctx = document.getElementById('dii-buy-sell-chart').getContext('2d');

            if (diiBuySellChart) {
                diiBuySellChart.destroy();
            }

            diiBuySellChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: 'Buy',
                            data: buyValues,
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Sell',
                            data: sellValues,
                            backgroundColor: 'rgba(255, 99, 132, 0.7)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'DII Buy vs Sell (in Cr)'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += formatNumber(context.raw);
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Amount (Cr)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    }
                }
            });
        }

        // Function to update data table
        function updateDataTable(dates, fiiDataMap, diiDataMap) {
            const tableBody = $('#fii-dii-table-body');
            tableBody.empty();

            dates.forEach(date => {
                const fiiData = fiiDataMap[date] || { buy_value: 0, sell_value: 0, net_value: 0 };
                const diiData = diiDataMap[date] || { buy_value: 0, sell_value: 0, net_value: 0 };

                const fiiNetClass = fiiData.net_value >= 0 ? 'text-success' : 'text-danger';
                const diiNetClass = diiData.net_value >= 0 ? 'text-success' : 'text-danger';

                tableBody.append(`
                    <tr>
                        <td>${date}</td>
                        <td>${formatNumber(fiiData.buy_value)}</td>
                        <td>${formatNumber(fiiData.sell_value)}</td>
                        <td class="${fiiNetClass}">${formatNumber(fiiData.net_value)}</td>
                        <td>${formatNumber(diiData.buy_value)}</td>
                        <td>${formatNumber(diiData.sell_value)}</td>
                        <td class="${diiNetClass}">${formatNumber(diiData.net_value)}</td>
                    </tr>
                `);
            });
        }

        // Function to start auto-refresh
        function startAutoRefresh() {
            if (!refreshInterval) {
                refreshInterval = setInterval(function() {
                    const days = $('#days-filter').val();
                    loadFiiDiiData(days);
                }, 1000);
                console.log('FII/DII auto-refresh started');
            }
        }

        // Function to stop auto-refresh
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                console.log('FII/DII auto-refresh stopped');
            }
        }

        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // Page is visible, start auto-refresh and load latest data
                const days = $('#days-filter').val();
                loadFiiDiiData(days);
                startAutoRefresh();
            } else {
                // Page is hidden, stop auto-refresh
                stopAutoRefresh();
            }
        });

        // Handle days filter change
        $('#days-filter').on('change', function() {
            const days = $(this).val();
            loadFiiDiiData(days);
        });

        // Clean up when page is unloaded
        $(window).on('beforeunload', function() {
            stopAutoRefresh();
        });

        // Function to handle when the FII/DII tab is activated
        function activateFiiDiiTab() {
            console.log('FII/DII tab activated');
            isTabActive = true;
            loadFiiDiiData($('#days-filter').val());
            startAutoRefresh();
        }

        // Function to handle when the FII/DII tab is deactivated
        function deactivateFiiDiiTab() {
            console.log('FII/DII tab deactivated');
            isTabActive = false;
            stopAutoRefresh();
        }

        // For standalone page
        if (window.location.pathname === '/fii-dii' && document.visibilityState === 'visible') {
            console.log('Loading FII/DII data for standalone page');
            isTabActive = true;
            loadFiiDiiData(30);
            startAutoRefresh();
        }

        // For tabbed interface - listen for tab activation
        $(document).on('shown.bs.tab', 'a[data-bs-toggle="tab"]', function (e) {
            const targetId = $(e.target).attr('href');
            if (targetId === '#fii-dii-tab') {
                activateFiiDiiTab();
            } else if (isTabActive) {
                deactivateFiiDiiTab();
            }
        });

        // For dashboard tabs without Bootstrap
        $('#dashboardTabs .nav-link').on('click', function() {
            if ($(this).attr('id') === 'fii-dii-tab') {
                activateFiiDiiTab();
            } else if (isTabActive) {
                deactivateFiiDiiTab();
            }
        });
    });
</script>
{% endblock %}
