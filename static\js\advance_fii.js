/**
 * Advanced FII Analysis JavaScript
 * Handles loading and displaying advanced FII data
 */

// Global variables
let advanceFiiData = null;
let advanceFiiRefreshInterval = null;
let advanceFiiLastUpdated = null;
let advanceFiiCurrentDate = null;
let advanceFiiCurrentCategory = 'all';
let advanceFiiCurrentSegment = 'all';
let advanceFiiCurrentView = 'all';

// Format number with commas and 2 decimal places
function formatNumber(num) {
    if (num === null || num === undefined) return '-';
    return new Intl.NumberFormat('en-IN', {
        maximumFractionDigits: 2,
        minimumFractionDigits: 2
    }).format(num);
}

// Format percentage
function formatPercentage(num) {
    if (num === null || num === undefined) return '-';
    return new Intl.NumberFormat('en-IN', {
        maximumFractionDigits: 2,
        minimumFractionDigits: 2,
        style: 'percent',
        signDisplay: 'exceptZero'
    }).format(num / 100);
}

// Get color based on value (positive/negative)
function getValueColor(value) {
    if (value === null || value === undefined || value === 0) return '';
    return value > 0 ? 'text-success' : 'text-danger';
}

// Get color based on view (bullish/bearish)
function getViewColor(view) {
    if (!view) return '';
    view = view.toUpperCase();
    if (view === 'BULLISH') return 'text-success';
    if (view === 'BEARISH') return 'text-danger';
    if (view === 'NEUTRAL' || view === 'INDECISIVE') return 'text-warning';
    return '';
}

// Get badge class based on strength
function getStrengthBadgeClass(strength) {
    if (!strength) return 'bg-secondary';
    strength = strength.toLowerCase();
    if (strength === 'strong') return 'bg-primary';
    if (strength === 'medium') return 'bg-info';
    if (strength === 'mild') return 'bg-secondary';
    return 'bg-secondary';
}

// Load available dates for the date filter
function loadAdvanceFiiDates() {
    $.ajax({
        url: '/api/advance-fii/dates',
        method: 'GET',
        success: function(dates) {
            const dateSelect = $('#advance-fii-date-filter');
            dateSelect.empty();

            // Sort dates in descending order (newest first)
            dates.sort().reverse();

            // Add options for each date
            dates.forEach(date => {
                const formattedDate = new Date(date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
                dateSelect.append(`<option value="${date}">${formattedDate}</option>`);
            });

            // If we have dates, load the first one
            if (dates.length > 0) {
                advanceFiiCurrentDate = dates[0];
                loadAdvanceFiiData(advanceFiiCurrentDate);
            } else {
                $('#advance-fii-loading').hide();
                $('#advance-fii-content').html('<div class="alert alert-warning">No data available</div>').show();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading Advanced FII dates:', error);
            $('#advance-fii-loading').hide();
            $('#advance-fii-content').html('<div class="alert alert-danger">Error loading data: ' + error + '</div>').show();
        }
    });
}

// Load Advanced FII data for a specific date
function loadAdvanceFiiData(date) {
    $('#advance-fii-loading').show();
    $('#advance-fii-content').hide();

    $.ajax({
        url: '/api/advance-fii',
        method: 'GET',
        data: { date: date },
        success: function(response) {
            advanceFiiData = response;
            advanceFiiLastUpdated = new Date();

            if (response.error) {
                $('#advance-fii-loading').hide();
                $('#advance-fii-content').html('<div class="alert alert-warning">' + response.error + '</div>').show();
                return;
            }

            // Update the data display
            updateAdvanceFiiDisplay();

            // Hide loading indicator and show content
            $('#advance-fii-loading').hide();
            $('#advance-fii-content').show();

            // Update last updated time
            $('#advance-fii-last-updated').text('Last updated: ' + advanceFiiLastUpdated.toLocaleTimeString());
        },
        error: function(xhr, status, error) {
            console.error('Error loading Advanced FII data:', error);
            $('#advance-fii-loading').hide();
            $('#advance-fii-content').html('<div class="alert alert-danger">Error loading data: ' + error + '</div>').show();
        }
    });
}

// Update the display with the current data
function updateAdvanceFiiDisplay() {
    if (!advanceFiiData || !advanceFiiData.data) return;

    // Get the data for the selected date
    const dateKey = advanceFiiCurrentDate;
    const dateData = advanceFiiData.data[dateKey];

    if (!dateData) {
        $('#advance-fii-content').html('<div class="alert alert-warning">No data available for selected date</div>').show();
        return;
    }

    // Update market summary
    updateMarketSummary(dateData);

    // Update cash section
    updateCashSection(dateData.cash);

    // Update futures section
    updateFuturesSection(dateData.future);

    // Update individual index futures section
    updateIndexFuturesSection(dateData.future);

    // Update amount-wise futures section
    updateAmountFuturesSection(dateData.future);

    // Update options section
    updateOptionsSection(dateData.option);

    // Apply filters
    applyAdvanceFiiFilters();
}

// Update market summary section
function updateMarketSummary(data) {
    $('#advance-fii-nifty').text(formatNumber(data.nifty));

    const niftyChangePercent = data.nifty_change_percent;
    const niftyChangeElement = $('#advance-fii-nifty-change');
    niftyChangeElement.text(formatPercentage(niftyChangePercent));
    niftyChangeElement.removeClass('text-success text-danger');
    niftyChangeElement.addClass(getValueColor(niftyChangePercent));

    $('#advance-fii-banknifty').text(formatNumber(data.banknifty));

    const bankniftyChangePercent = data.banknifty_change_percent;
    const bankniftyChangeElement = $('#advance-fii-banknifty-change');
    bankniftyChangeElement.text(formatPercentage(bankniftyChangePercent));
    bankniftyChangeElement.removeClass('text-success text-danger');
    bankniftyChangeElement.addClass(getValueColor(bankniftyChangePercent));

    // Format date
    const dateObj = new Date(data.date);
    const formattedDate = dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    $('#advance-fii-date').text(formattedDate);

    // Format next market open
    if (data.next_market_open) {
        const nextMarketOpenDate = new Date(data.next_market_open);
        const formattedNextMarketOpen = nextMarketOpenDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        $('#advance-fii-next-market-open').text(formattedNextMarketOpen);
    } else {
        $('#advance-fii-next-market-open').text('-');
    }
}

// Update cash section
function updateCashSection(cashData) {
    const tableBody = $('#advance-fii-cash-table');
    tableBody.empty();

    if (!cashData) {
        tableBody.append('<tr><td colspan="7" class="text-center">No cash data available</td></tr>');
        return;
    }

    // Add row for each category
    for (const category in cashData) {
        const data = cashData[category];
        const row = `
            <tr data-category="${category.toLowerCase()}" data-segment="cash" data-view="${data.net_view ? data.net_view.toLowerCase() : ''}">
                <td class="text-uppercase">${category}</td>
                <td>${formatNumber(data.buy)}</td>
                <td>${formatNumber(data.sell)}</td>
                <td class="${getValueColor(data.buy_sell_difference)}">${formatNumber(data.buy_sell_difference)}</td>
                <td>${data.net_action || '-'}</td>
                <td class="${getViewColor(data.net_view)}">${data.net_view || '-'}</td>
                <td><span class="badge ${getStrengthBadgeClass(data.net_view_strength)}">${data.net_view_strength || '-'}</span></td>
            </tr>
        `;
        tableBody.append(row);
    }
}

// Update futures section
function updateFuturesSection(futureData) {
    const indexTableBody = $('#advance-fii-futures-index-table');
    const stockTableBody = $('#advance-fii-futures-stock-table');
    indexTableBody.empty();
    stockTableBody.empty();

    if (!futureData) {
        indexTableBody.append('<tr><td colspan="9" class="text-center">No futures data available</td></tr>');
        stockTableBody.append('<tr><td colspan="6" class="text-center">No futures data available</td></tr>');
        return;
    }

    // Add row for each category in index futures
    for (const category in futureData) {
        const data = futureData[category];

        if (data['quantity-wise']) {
            const qtyData = data['quantity-wise'];
            const indexRow = `
                <tr data-category="${category.toLowerCase()}" data-segment="future" data-view="${qtyData.net_view ? qtyData.net_view.toLowerCase() : ''}">
                    <td class="text-uppercase">${category}</td>
                    <td>${formatNumber(qtyData.outstanding_oi)}</td>
                    <td>${formatNumber(qtyData.net_oi)}</td>
                    <td>${qtyData.net_action || '-'}</td>
                    <td class="${getViewColor(qtyData.net_view)}">${qtyData.net_view || '-'}</td>
                    <td><span class="badge ${getStrengthBadgeClass(qtyData.net_view_strength)}">${qtyData.net_view_strength || '-'}</span></td>
                    <td class="${getViewColor(qtyData.nifty_net_view)}">${qtyData.nifty_net_view || '-'} <span class="badge ${getStrengthBadgeClass(qtyData.nifty_net_view_strength)}">${qtyData.nifty_net_view_strength || '-'}</span></td>
                    <td class="${getViewColor(qtyData.banknifty_net_view)}">${qtyData.banknifty_net_view || '-'} <span class="badge ${getStrengthBadgeClass(qtyData.banknifty_net_view_strength)}">${qtyData.banknifty_net_view_strength || '-'}</span></td>
                    <td class="${getViewColor(qtyData.finnifty_net_view)}">${qtyData.finnifty_net_view || '-'} <span class="badge ${getStrengthBadgeClass(qtyData.finnifty_net_view_strength)}">${qtyData.finnifty_net_view_strength || '-'}</span></td>
                </tr>
            `;
            indexTableBody.append(indexRow);
        }

        // Stock futures data
        const stockRow = `
            <tr data-category="${category.toLowerCase()}" data-segment="future" data-view="${data.futures_stock_net_view ? data.futures_stock_net_view.toLowerCase() : ''}">
                <td class="text-uppercase">${category}</td>
                <td>${formatNumber(data.futures_stock_outstanding_oi)}</td>
                <td>${formatNumber(data.futures_stock_net_oi)}</td>
                <td>${data.futures_stock_net_action || '-'}</td>
                <td class="${getViewColor(data.futures_stock_net_view)}">${data.futures_stock_net_view || '-'}</td>
                <td><span class="badge ${getStrengthBadgeClass(data.futures_stock_net_view_strength)}">${data.futures_stock_net_view_strength || '-'}</span></td>
            </tr>
        `;
        stockTableBody.append(stockRow);
    }
}

// Update options section
function updateOptionsSection(optionData) {
    const callTableBody = $('#advance-fii-options-call-table');
    const putTableBody = $('#advance-fii-options-put-table');
    const overallTableBody = $('#advance-fii-options-overall-table');
    callTableBody.empty();
    putTableBody.empty();
    overallTableBody.empty();

    if (!optionData) {
        callTableBody.append('<tr><td colspan="10" class="text-center">No options data available</td></tr>');
        putTableBody.append('<tr><td colspan="10" class="text-center">No options data available</td></tr>');
        overallTableBody.append('<tr><td colspan="7" class="text-center">No options data available</td></tr>');
        return;
    }

    // Add row for each category
    for (const category in optionData) {
        const data = optionData[category];

        // Call options
        if (data.call) {
            const callData = data.call;
            const callRow = `
                <tr data-category="${category.toLowerCase()}" data-segment="option" data-view="${callData.net_oi_change_view ? callData.net_oi_change_view.toLowerCase() : ''}">
                    <td class="text-uppercase">${category}</td>
                    <td>${formatNumber(callData.long.oi_current)}</td>
                    <td class="${getValueColor(callData.long.oi_change)}">${formatNumber(callData.long.oi_change)}</td>
                    <td>${formatNumber(callData.short.oi_current)}</td>
                    <td class="${getValueColor(callData.short.oi_change)}">${formatNumber(callData.short.oi_change)}</td>
                    <td>${formatNumber(callData.net_oi)}</td>
                    <td class="${getValueColor(callData.net_oi_change)}">${formatNumber(callData.net_oi_change)}</td>
                    <td>${callData.net_oi_change_action || '-'}</td>
                    <td class="${getViewColor(callData.net_oi_change_view)}">${callData.net_oi_change_view || '-'}</td>
                    <td><span class="badge ${getStrengthBadgeClass(callData.net_oi_change_view_strength)}">${callData.net_oi_change_view_strength || '-'}</span></td>
                </tr>
            `;
            callTableBody.append(callRow);
        }

        // Put options
        if (data.put) {
            const putData = data.put;
            const putRow = `
                <tr data-category="${category.toLowerCase()}" data-segment="option" data-view="${putData.net_oi_change_view ? putData.net_oi_change_view.toLowerCase() : ''}">
                    <td class="text-uppercase">${category}</td>
                    <td>${formatNumber(putData.long.oi_current)}</td>
                    <td class="${getValueColor(putData.long.oi_change)}">${formatNumber(putData.long.oi_change)}</td>
                    <td>${formatNumber(putData.short.oi_current)}</td>
                    <td class="${getValueColor(putData.short.oi_change)}">${formatNumber(putData.short.oi_change)}</td>
                    <td>${formatNumber(putData.net_oi)}</td>
                    <td class="${getValueColor(putData.net_oi_change)}">${formatNumber(putData.net_oi_change)}</td>
                    <td>${putData.net_oi_change_action || '-'}</td>
                    <td class="${getViewColor(putData.net_oi_change_view)}">${putData.net_oi_change_view || '-'}</td>
                    <td><span class="badge ${getStrengthBadgeClass(putData.net_oi_change_view_strength)}">${putData.net_oi_change_view_strength || '-'}</span></td>
                </tr>
            `;
            putTableBody.append(putRow);
        }

        // Overall options
        const overallRow = `
            <tr data-category="${category.toLowerCase()}" data-segment="option" data-view="${data.overall_net_oi_change_view ? data.overall_net_oi_change_view.toLowerCase() : ''}">
                <td class="text-uppercase">${category}</td>
                <td>${formatNumber(data.overall_net_oi)}</td>
                <td class="${getValueColor(data.overall_net_oi_change)}">${formatNumber(data.overall_net_oi_change)}</td>
                <td>${data.overall_net_oi_change_action || '-'}</td>
                <td class="${getViewColor(data.overall_net_oi_change_view)}">${data.overall_net_oi_change_view || '-'}</td>
                <td><span class="badge ${getStrengthBadgeClass(data.overall_net_oi_change_view_strength)}">${data.overall_net_oi_change_view_strength || '-'}</span></td>
                <td class="${getViewColor(data.overall_net_oi_change_view_summary)}">${data.overall_net_oi_change_view_summary || '-'}</td>
            </tr>
        `;
        overallTableBody.append(overallRow);
    }
}

// Update individual index futures section
function updateIndexFuturesSection(futureData) {
    const indices = ['nifty', 'banknifty', 'finnifty', 'midcpnifty', 'niftynxt50'];

    // Process each index
    indices.forEach(index => {
        const tableBody = $(`#${index}-futures-table`);
        tableBody.empty();

        if (!futureData) {
            tableBody.append(`<tr><td colspan="6" class="text-center">No ${index.toUpperCase()} futures data available</td></tr>`);
            return;
        }

        // Add row for each category
        for (const category in futureData) {
            const data = futureData[category];

            // Skip if no quantity-wise data
            if (!data['quantity-wise']) continue;

            const qtyData = data['quantity-wise'];
            const amountData = data['amount-wise'] || {};

            // Skip if no data for this index
            if (!qtyData[`${index}_net_oi`] && qtyData[`${index}_net_oi`] !== 0) continue;

            const row = `
                <tr data-category="${category.toLowerCase()}" data-segment="future" data-view="${qtyData[`${index}_net_view`] ? qtyData[`${index}_net_view`].toLowerCase() : ''}">
                    <td class="text-uppercase">${category}</td>
                    <td>${formatNumber(qtyData[`${index}_net_oi`])}</td>
                    <td>${qtyData[`${index}_net_action`] || '-'}</td>
                    <td class="${getViewColor(qtyData[`${index}_net_view`])}">${qtyData[`${index}_net_view`] || '-'}</td>
                    <td><span class="badge ${getStrengthBadgeClass(qtyData[`${index}_net_view_strength`])}">${qtyData[`${index}_net_view_strength`] || '-'}</span></td>
                    <td>${formatNumber(amountData[`${index}_net_oi`] || 0)}</td>
                </tr>
            `;
            tableBody.append(row);
        }

        // If no rows were added, show a message
        if (tableBody.find('tr').length === 0) {
            tableBody.append(`<tr><td colspan="6" class="text-center">No ${index.toUpperCase()} futures data available</td></tr>`);
        }
    });
}

// Update amount-wise futures section
function updateAmountFuturesSection(futureData) {
    const tableBody = $('#amount-futures-table');
    tableBody.empty();

    if (!futureData) {
        tableBody.append('<tr><td colspan="8" class="text-center">No amount-wise futures data available</td></tr>');
        return;
    }

    // Add row for each category
    for (const category in futureData) {
        const data = futureData[category];

        // Skip if no amount-wise data
        if (!data['amount-wise']) continue;

        const amountData = data['amount-wise'];

        const row = `
            <tr data-category="${category.toLowerCase()}" data-segment="future" data-view="${amountData.net_view ? amountData.net_view.toLowerCase() : ''}">
                <td class="text-uppercase">${category}</td>
                <td>${formatNumber(amountData.net_oi || 0)}</td>
                <td>${formatNumber(amountData.nifty_net_oi || 0)}</td>
                <td>${formatNumber(amountData.banknifty_net_oi || 0)}</td>
                <td>${formatNumber(amountData.finnifty_net_oi || 0)}</td>
                <td>${formatNumber(amountData.midcpnifty_net_oi || 0)}</td>
                <td>${formatNumber(amountData.niftynxt50_net_oi || 0)}</td>
                <td class="${getViewColor(amountData.net_view)}">${amountData.net_view || '-'}</td>
            </tr>
        `;
        tableBody.append(row);
    }

    // If no rows were added, show a message
    if (tableBody.find('tr').length === 0) {
        tableBody.append('<tr><td colspan="8" class="text-center">No amount-wise futures data available</td></tr>');
    }
}

// Apply filters to the displayed data
function applyAdvanceFiiFilters() {
    // Get current filter values
    const category = advanceFiiCurrentCategory;
    const segment = advanceFiiCurrentSegment;
    const view = advanceFiiCurrentView;

    // Show all rows first
    $('tr[data-category]').show();

    // Apply category filter if not 'all'
    if (category !== 'all') {
        $('tr[data-category]').not(`tr[data-category="${category}"]`).hide();
    }

    // Apply segment filter if not 'all'
    if (segment !== 'all') {
        $('tr[data-segment]').not(`tr[data-segment="${segment}"]`).hide();

        // Show/hide section cards based on segment filter
        if (segment === 'cash') {
            $('#cash-section').show();
            $('#futures-section, #index-futures-section, #amount-futures-section, #options-section').hide();
        } else if (segment === 'future') {
            $('#futures-section, #index-futures-section, #amount-futures-section').show();
            $('#cash-section, #options-section').hide();
        } else if (segment === 'option') {
            $('#options-section').show();
            $('#cash-section, #futures-section, #index-futures-section, #amount-futures-section').hide();
        } else {
            $('#cash-section, #futures-section, #index-futures-section, #amount-futures-section, #options-section').show();
        }
    } else {
        // Show all sections
        $('#cash-section, #futures-section, #index-futures-section, #amount-futures-section, #options-section').show();
    }

    // Apply view filter if not 'all'
    if (view !== 'all') {
        $('tr[data-view]').not(`tr[data-view="${view}"]`).hide();
    }

    // Check if any rows are visible in each table, if not show a message
    const tables = [
        '#advance-fii-cash-table',
        '#advance-fii-futures-index-table',
        '#advance-fii-futures-stock-table',
        '#nifty-futures-table',
        '#banknifty-futures-table',
        '#finnifty-futures-table',
        '#midcpnifty-futures-table',
        '#niftynxt50-futures-table',
        '#amount-futures-table',
        '#advance-fii-options-call-table',
        '#advance-fii-options-put-table',
        '#advance-fii-options-overall-table'
    ];

    tables.forEach(tableSelector => {
        const table = $(tableSelector);
        const visibleRows = table.find('tr:visible').length;

        if (visibleRows === 0) {
            table.append(`<tr class="no-data-row"><td colspan="${table.find('th').length}" class="text-center">No data matching the selected filters</td></tr>`);
        } else {
            table.find('.no-data-row').remove();
        }
    });
}

// Start auto-refresh for Advanced FII data
function startAdvanceFiiAutoRefresh() {
    // Clear any existing interval
    if (advanceFiiRefreshInterval) {
        clearInterval(advanceFiiRefreshInterval);
    }

    // Set up new interval (15 minutes = 900000 ms)
    advanceFiiRefreshInterval = setInterval(function() {
        if (advanceFiiCurrentDate) {
            loadAdvanceFiiData(advanceFiiCurrentDate);
        }
    }, 900000);

    console.log('Advanced FII auto-refresh started (15 minute interval)');
}

// Stop auto-refresh for Advanced FII data
function stopAdvanceFiiAutoRefresh() {
    if (advanceFiiRefreshInterval) {
        clearInterval(advanceFiiRefreshInterval);
        advanceFiiRefreshInterval = null;
        console.log('Advanced FII auto-refresh stopped');
    }
}

// Initialize Advanced FII functionality
$(document).ready(function() {
    // Handle date filter change
    $('#advance-fii-date-filter').on('change', function() {
        advanceFiiCurrentDate = $(this).val();
        loadAdvanceFiiData(advanceFiiCurrentDate);
    });

    // Handle category filter change
    $('#advance-fii-category-filter').on('change', function() {
        advanceFiiCurrentCategory = $(this).val();
        applyAdvanceFiiFilters();
    });

    // Handle segment filter change
    $('#advance-fii-segment-filter').on('change', function() {
        advanceFiiCurrentSegment = $(this).val();
        applyAdvanceFiiFilters();
    });

    // Handle view filter change
    $('#advance-fii-view-filter').on('change', function() {
        advanceFiiCurrentView = $(this).val();
        applyAdvanceFiiFilters();
    });

    // Load data when the tab is shown
    $('#advance-fii-tab').on('shown.bs.tab', function() {
        if (!advanceFiiData) {
            loadAdvanceFiiDates();
        }
        startAdvanceFiiAutoRefresh();
    });

    // Stop auto-refresh when the tab is hidden
    $('#advance-fii-tab').on('hidden.bs.tab', function() {
        stopAdvanceFiiAutoRefresh();
    });
});
