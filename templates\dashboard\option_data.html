{% extends "base.html" %}

{% block title %}Option Data Dashboard{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
<style>
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    .table th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 1;
    }
    .nav-tabs {
        margin-bottom: 20px;
    }
    .highlight-row {
        background-color: #e6f7ff !important;
    }
    .call-positive {
        background-color: rgba(0, 255, 0, 0.2) !important;
        color: #006400 !important;
        font-weight: bold;
    }
    .call-negative {
        background-color: rgba(255, 0, 0, 0.2) !important;
        color: #8B0000 !important;
        font-weight: bold;
    }
    .put-positive {
        background-color: rgba(0, 255, 0, 0.2) !important;
        color: #006400 !important;
        font-weight: bold;
    }
    .put-negative {
        background-color: rgba(255, 0, 0, 0.2) !important;
        color: #8B0000 !important;
        font-weight: bold;
    }
    .atm-strike {
        font-weight: bold;
        background-color: #fffde7;
    }
    #refreshStatus {
        font-size: 12px;
        color: #666;
    }
    .summary-card {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .summary-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    .summary-value {
        font-weight: bold;
        font-size: 1.4rem;
        color: #343a40;
    }
    .summary-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    /* Different colors for different types of cards */
    .summary-card.price-card {
        border-left-color: #28a745;
    }
    .summary-card.ratio-card {
        border-left-color: #fd7e14;
    }
    .summary-card.oi-card {
        border-left-color: #007bff;
    }
    .summary-card.strike-card {
        border-left-color: #6f42c1;
    }
    .refresh-btn {
        cursor: pointer;
    }
    .date-selector {
        max-width: 200px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Option Chain Analysis Dashboard</h4>
            </div>
            <div class="card-body">
                <p class="lead">Track and analyze option chain data in real-time with our powerful dashboard.</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-4">
        <div class="input-group">
            <span class="input-group-text">Symbol</span>
            <select id="symbolSelector" class="form-select">
                <option value="">Loading symbols...</option>
            </select>
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-check form-switch mt-2">
            <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
            <label class="form-check-label" for="autoRefreshToggle">Auto-refresh (1 second)</label>
            <span id="refreshStatus" class="ms-2"></span>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <button id="refreshBtn" class="btn btn-outline-primary">
            <i class="bi bi-arrow-clockwise"></i> Refresh Data
        </button>
    </div>
</div>

<ul class="nav nav-tabs" id="dataTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="live-tab" data-bs-toggle="tab" data-bs-target="#live" type="button" role="tab" aria-controls="live" aria-selected="true">Live Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="historical-tab" data-bs-toggle="tab" data-bs-target="#historical" type="button" role="tab" aria-controls="historical" aria-selected="false">Historical Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="oi-tab" data-bs-toggle="tab" data-bs-target="#oi" type="button" role="tab" aria-controls="oi" aria-selected="false">OI Data</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="oi-chart-tab" data-bs-toggle="tab" data-bs-target="#oi-chart" type="button" role="tab" aria-controls="oi-chart" aria-selected="false">OI Chart</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="orders-log-tab" data-bs-toggle="tab" data-bs-target="#orders-log" type="button" role="tab" aria-controls="orders-log" aria-selected="false">Orders Log</button>
    </li>
</ul>

<div class="tab-content" id="dataTabsContent">
    <!-- Live Data Tab -->
    <div class="tab-pane fade show active" id="live" role="tabpanel" aria-labelledby="live-tab">
        <div class="row" id="liveSummaryCards">
            <!-- Summary cards will be inserted here -->
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Option Chain</h5>
                <span id="lastUpdated"></span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="optionChainTable">
                        <thead>
                            <tr>
                                <th colspan="7" class="text-center bg-light">CALLS</th>
                                <th class="text-center bg-warning">STRIKE</th>
                                <th colspan="7" class="text-center bg-light">PUTS</th>
                            </tr>
                            <tr>
                                <th>OI</th>
                                <th>OI Chg</th>
                                <th>Open</th>
                                <th>High</th>
                                <th>Low</th>
                                <th>LTP</th>
                                <th>Chg</th>
                                <th class="text-center bg-warning">PRICE</th>
                                <th>Chg</th>
                                <th>LTP</th>
                                <th>Low</th>
                                <th>High</th>
                                <th>Open</th>
                                <th>OI Chg</th>
                                <th>OI</th>
                            </tr>
                        </thead>
                        <tbody id="optionChainBody">
                            <!-- Option chain data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Historical Data Tab -->
    <div class="tab-pane fade" id="historical" role="tabpanel" aria-labelledby="historical-tab">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">Date</span>
                    <select id="dateSelector" class="form-select date-selector">
                        <option value="">Select a date</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">Time</span>
                    <input type="time" id="timeSelector" class="form-control" step="1">
                </div>
            </div>
            <div class="col-md-4">
                <button id="loadHistoricalBtn" class="btn btn-primary">
                    <i class="bi bi-search"></i> Load Data
                </button>
            </div>
        </div>

        <div class="row" id="historicalSummaryCards">
            <!-- Historical summary cards will be inserted here -->
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Historical Option Chain</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="historicalOptionChainTable">
                        <thead>
                            <tr>
                                <th colspan="7" class="text-center bg-light">CALLS</th>
                                <th class="text-center bg-warning">STRIKE</th>
                                <th colspan="7" class="text-center bg-light">PUTS</th>
                            </tr>
                            <tr>
                                <th>OI</th>
                                <th>OI Chg</th>
                                <th>Open</th>
                                <th>High</th>
                                <th>Low</th>
                                <th>LTP</th>
                                <th>Chg</th>
                                <th class="text-center bg-warning">PRICE</th>
                                <th>Chg</th>
                                <th>LTP</th>
                                <th>Low</th>
                                <th>High</th>
                                <th>Open</th>
                                <th>OI Chg</th>
                                <th>OI</th>
                            </tr>
                        </thead>
                        <tbody id="historicalOptionChainBody">
                            <!-- Historical option chain data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- OI Data Tab -->
    <div class="tab-pane fade" id="oi" role="tabpanel" aria-labelledby="oi-tab">
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Open Interest Data for All Symbols</h5>
                <span id="oiLastUpdated"></span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="oiDataTable">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>FUT OI</th>
                                <th>FUT Change in OI</th>
                                <th>Max Call Change in OI Strike</th>
                                <th>Max Call Change in OI</th>
                                <th>Max Call OI Strike</th>
                                <th>Max Call OI</th>
                                <th>SPOT LTP</th>
                                <th>Max Put OI</th>
                                <th>Max Put OI Strike</th>
                                <th>Max Put Change in OI</th>
                                <th>Max Put Change in OI Strike</th>
                            </tr>
                        </thead>
                        <tbody id="oiDataBody">
                            <!-- OI data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- OI Chart Tab -->
    <div class="tab-pane fade" id="oi-chart" role="tabpanel" aria-labelledby="oi-chart-tab">
        <div class="row mt-3 mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">Chart Type</span>
                    <select id="chartTypeSelector" class="form-select">
                        <option value="futOi">Futures OI</option>
                        <option value="futOiChange">Futures OI Change</option>
                        <option value="maxOi">Max OI</option>
                        <option value="maxOiChange">Max OI Change</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <span id="oiChartLastUpdated"></span>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0" id="chartTitle">Open Interest Chart</h5>
            </div>
            <div class="card-body">
                <div id="oiChart" style="height:60vh; width:100%"></div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Call vs Put OI Ratio</h5>
                    </div>
                    <div class="card-body">
                        <div id="pcrChart" style="height:30vh; width:100%"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Max OI Strikes</h5>
                    </div>
                    <div class="card-body">
                        <div id="strikesChart" style="height:30vh; width:100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Log Tab -->
    <div class="tab-pane fade" id="orders-log" role="tabpanel" aria-labelledby="orders-log-tab">
        <div class="row mt-3 mb-3">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">Date</span>
                    <select id="orderLogDateSelector" class="form-select">
                        <option value="">All Dates</option>
                        <!-- Dates will be loaded dynamically -->
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">Symbol</span>
                    <select id="orderLogSymbolSelector" class="form-select">
                        <option value="">All Symbols</option>
                        <!-- Symbols will be loaded dynamically -->
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">Rows</span>
                    <select id="orderLogLimitSelector" class="form-select">
                        <option value="100">100</option>
                        <option value="200">200</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                        <option value="0">All</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3 text-end">
                <button id="orderLogRefreshBtn" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </button>
                <span id="orderLogLastUpdated"></span>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Trading Orders Log</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-hover" id="orderLogTable">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Symbol</th>
                                <th>Order Type</th>
                                <th>Option Type</th>
                                <th>Strike</th>
                                <th>Spot Price</th>
                                <th>Option Price</th>
                                <th>P&L</th>
                                <th>Support</th>
                                <th>Resistance</th>
                                <th>Status</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody id="orderLogBody">
                            <!-- Order log data will be inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let autoRefresh = true;
    let refreshInterval;
    let oiDataRefreshInterval;
    let orderLogRefreshInterval;
    let currentSymbol = '';
    let historicalData = {};
    let currentOrderLogDate = '';
    let currentOrderLogSymbol = '';
    let oiChart = null;
    let pcrChart = null;
    let strikesChart = null;

    // DOM elements
    const symbolSelector = document.getElementById('symbolSelector');
    const autoRefreshToggle = document.getElementById('autoRefreshToggle');
    const refreshBtn = document.getElementById('refreshBtn');
    const refreshStatus = document.getElementById('refreshStatus');
    const lastUpdated = document.getElementById('lastUpdated');
    const liveSummaryCards = document.getElementById('liveSummaryCards');
    const optionChainBody = document.getElementById('optionChainBody');
    const dateSelector = document.getElementById('dateSelector');
    const timeSelector = document.getElementById('timeSelector');
    const historicalSummaryCards = document.getElementById('historicalSummaryCards');
    const historicalOptionChainBody = document.getElementById('historicalOptionChainBody');
    const orderLogDateSelector = document.getElementById('orderLogDateSelector');
    const orderLogSymbolSelector = document.getElementById('orderLogSymbolSelector');
    const orderLogLimitSelector = document.getElementById('orderLogLimitSelector');
    const orderLogRefreshBtn = document.getElementById('orderLogRefreshBtn');
    const orderLogBody = document.getElementById('orderLogBody');
    const orderLogLastUpdated = document.getElementById('orderLogLastUpdated');

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        loadSymbols();

        // Initialize charts
        initCharts();

        // Load OI data on page load and start auto-refresh
        loadOIData();
        startOIDataAutoRefresh();

        // Initialize Orders Log tab data
        loadOrderLogDates();
        loadOrderLogSymbols();
        loadOrderLogData();
        startOrderLogAutoRefresh(); // Start auto-refresh for order log

        // Clean up intervals when page is unloaded
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
            stopOrderLogAutoRefresh();
            stopOIDataAutoRefresh();
        });

        // Event listeners
        symbolSelector.addEventListener('change', function() {
            currentSymbol = this.value;
            if (currentSymbol) {
                loadLiveData();
                loadDates();
                startAutoRefresh();
            }
        });

        autoRefreshToggle.addEventListener('change', function() {
            autoRefresh = this.checked;
            if (autoRefresh) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        refreshBtn.addEventListener('click', function() {
            if (currentSymbol) {
                loadLiveData();
            }
        });

        // Tab change event
        document.getElementById('dataTabs').addEventListener('shown.bs.tab', function(e) {
            // First stop all auto-refresh intervals
            stopOrderLogAutoRefresh();
            stopOIDataAutoRefresh();

            if (e.target.id === 'historical-tab') {
                if (currentSymbol) {
                    loadDates();
                }
            } else if (e.target.id === 'oi-tab') {
                loadOIData();
                startOIDataAutoRefresh(); // Start auto-refresh for OI data
            } else if (e.target.id === 'oi-chart-tab') {
                // Initialize charts if needed
                if (!oiChart || !pcrChart || !strikesChart) {
                    initCharts();
                }
                // Load data for charts
                loadOIData();
                startOIDataAutoRefresh(); // Start auto-refresh for OI charts
            } else if (e.target.id === 'orders-log-tab') {
                // Load order log data and start auto-refresh
                loadOrderLogDates();
                loadOrderLogSymbols();
                loadOrderLogData();
                startOrderLogAutoRefresh();
            } else if (e.target.id === 'live-tab') {
                // Make sure live data auto-refresh is running
                if (currentSymbol) {
                    loadLiveData();
                    startAutoRefresh();
                }
            }
        });

        // Date selector for historical data
        if (dateSelector) {
            dateSelector.addEventListener('change', function() {
                // Set default time to 09:15:00 (market opening time)
                if (timeSelector && !timeSelector.value) {
                    timeSelector.value = '09:15:00';
                }
            });
        }

        // Load historical data button
        const loadHistoricalBtn = document.getElementById('loadHistoricalBtn');
        if (loadHistoricalBtn) {
            loadHistoricalBtn.addEventListener('click', function() {
                if (dateSelector.value) {
                    // Default to 09:15:00 if no time is selected
                    const time = timeSelector.value || '09:15:00';
                    loadHistoricalData(dateSelector.value, time);
                } else {
                    alert('Please select a date first');
                }
            });
        }

        // Order log filters
        if (orderLogDateSelector) {
            orderLogDateSelector.addEventListener('change', function() {
                currentOrderLogDate = this.value;
                loadOrderLogData();
            });
        }

        if (orderLogSymbolSelector) {
            orderLogSymbolSelector.addEventListener('change', function() {
                currentOrderLogSymbol = this.value;
                loadOrderLogData();
            });
        }

        if (orderLogLimitSelector) {
            orderLogLimitSelector.addEventListener('change', function() {
                loadOrderLogData();
            });
        }

        if (orderLogRefreshBtn) {
            orderLogRefreshBtn.addEventListener('click', function() {
                loadOrderLogData();
            });
        }

        // Chart type selector
        const chartTypeSelector = document.getElementById('chartTypeSelector');
        if (chartTypeSelector) {
            chartTypeSelector.addEventListener('change', function() {
                // Reload OI data to update charts with the new chart type
                loadOIData();
            });
        }
    });

    // Function to load symbols
    function loadSymbols() {
        fetch('/api/symbols')
            .then(response => response.json())
            .then(data => {
                symbolSelector.innerHTML = '<option value="">Select a symbol</option>';
                data.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    symbolSelector.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading symbols:', error));
    }

    // Function to load live data
    function loadLiveData() {
        if (!currentSymbol) return;

        fetch(`/api/latest/${currentSymbol}`)
            .then(response => response.json())
            .then(data => {
                // Update UI with live data
                updateLiveData(data);
            })
            .catch(error => console.error('Error loading live data:', error));
    }

    // Function to update live data in the UI
    function updateLiveData(data) {
        // Update last updated timestamp
        lastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

        // Display summary cards
        displaySummaryCards(data.summary, liveSummaryCards);

        // Display option chain
        optionChainBody.innerHTML = '';

        // Sort by strike price
        data.option_chain.sort((a, b) => parseFloat(a.strike) - parseFloat(b.strike));

        // Find ATM strike (closest to spot price)
        const spotPrice = parseFloat(data.summary.Spot_LTP);
        let atmStrike = data.option_chain.length > 0 ? data.option_chain[0].strike : 0;
        let minDiff = data.option_chain.length > 0 ? Math.abs(parseFloat(data.option_chain[0].strike) - spotPrice) : Infinity;

        for (const option of data.option_chain) {
            const strike = parseFloat(option.strike);
            const diff = Math.abs(strike - spotPrice);
            if (diff < minDiff) {
                minDiff = diff;
                atmStrike = option.strike;
            }

            const row = document.createElement('tr');

            // Check if this is the ATM strike
            if (option.strike === atmStrike) {
                row.classList.add('atm-strike');
            }

            // CE data
            const ceData = option.data;

            // CE OI
            const ceOI = ceData.CE_OI || 0;
            const ceOICell = document.createElement('td');
            ceOICell.textContent = ceOI.toLocaleString();
            row.appendChild(ceOICell);

            // CE OI Change
            const ceOIChange = ceData.CE_OI_Change || 0;
            const ceOIChangeCell = document.createElement('td');
            ceOIChangeCell.textContent = ceOIChange.toLocaleString();
            if (ceOIChange > 0) ceOIChangeCell.classList.add('call-positive');
            if (ceOIChange < 0) ceOIChangeCell.classList.add('call-negative');
            row.appendChild(ceOIChangeCell);

            // CE Open
            const ceOpen = ceData.CE_Open || 0;
            const ceOpenCell = document.createElement('td');
            ceOpenCell.textContent = ceOpen.toLocaleString();
            row.appendChild(ceOpenCell);

            // CE High
            const ceHigh = ceData.CE_High || 0;
            const ceHighCell = document.createElement('td');
            ceHighCell.textContent = ceHigh.toLocaleString();
            row.appendChild(ceHighCell);

            // CE Low
            const ceLow = ceData.CE_Low || 0;
            const ceLowCell = document.createElement('td');
            ceLowCell.textContent = ceLow.toLocaleString();
            row.appendChild(ceLowCell);

            // CE LTP
            const ceLTP = ceData.CE_LTP || 0;
            const ceLTPCell = document.createElement('td');
            ceLTPCell.textContent = ceLTP.toLocaleString();
            row.appendChild(ceLTPCell);

            // CE LTP Change
            const ceLTPChange = ceData.CE_LTP_Change || 0;
            const ceLTPChangeCell = document.createElement('td');
            ceLTPChangeCell.textContent = ceLTPChange.toLocaleString();
            if (ceLTPChange > 0) ceLTPChangeCell.classList.add('call-positive');
            if (ceLTPChange < 0) ceLTPChangeCell.classList.add('call-negative');
            row.appendChild(ceLTPChangeCell);

            // Strike Price
            const strikeCell = document.createElement('td');
            strikeCell.textContent = option.strike;
            strikeCell.classList.add('text-center', 'bg-warning');
            row.appendChild(strikeCell);

            // PE data
            const peData = option.data;

            // PE LTP Change
            const peLTPChange = peData.PE_LTP_Change || 0;
            const peLTPChangeCell = document.createElement('td');
            peLTPChangeCell.textContent = peLTPChange.toLocaleString();
            if (peLTPChange > 0) peLTPChangeCell.classList.add('put-positive');
            if (peLTPChange < 0) peLTPChangeCell.classList.add('put-negative');
            row.appendChild(peLTPChangeCell);

            // PE LTP
            const peLTP = peData.PE_LTP || 0;
            const peLTPCell = document.createElement('td');
            peLTPCell.textContent = peLTP.toLocaleString();
            row.appendChild(peLTPCell);

            // PE Low
            const peLow = peData.PE_Low || 0;
            const peLowCell = document.createElement('td');
            peLowCell.textContent = peLow.toLocaleString();
            row.appendChild(peLowCell);

            // PE High
            const peHigh = peData.PE_High || 0;
            const peHighCell = document.createElement('td');
            peHighCell.textContent = peHigh.toLocaleString();
            row.appendChild(peHighCell);

            // PE Open
            const peOpen = peData.PE_Open || 0;
            const peOpenCell = document.createElement('td');
            peOpenCell.textContent = peOpen.toLocaleString();
            row.appendChild(peOpenCell);

            // PE OI Change
            const peOIChange = peData.PE_OI_Change || 0;
            const peOIChangeCell = document.createElement('td');
            peOIChangeCell.textContent = peOIChange.toLocaleString();
            if (peOIChange > 0) peOIChangeCell.classList.add('put-positive');
            if (peOIChange < 0) peOIChangeCell.classList.add('put-negative');
            row.appendChild(peOIChangeCell);

            // PE OI
            const peOI = peData.PE_OI || 0;
            const peOICell = document.createElement('td');
            peOICell.textContent = peOI.toLocaleString();
            row.appendChild(peOICell);

            optionChainBody.appendChild(row);
        }
    }

    // Function to display summary cards
    function displaySummaryCards(summary, container) {
        container.innerHTML = '';

        // Create cards for important metrics
        const metrics = [
            { key: 'Spot_LTP', label: 'Spot LTP', format: 'number', cardClass: 'price-card', icon: 'bi-graph-up' },
            { key: 'PCR', label: 'Put/Call Ratio', format: 'number', cardClass: 'ratio-card', icon: 'bi-pie-chart' },
            { key: 'Max_Pain', label: 'Max Pain', format: 'number', cardClass: 'strike-card', icon: 'bi-bullseye' },
            { key: 'FUT_OI', label: 'Futures OI', format: 'number', cardClass: 'oi-card', icon: 'bi-bar-chart-line' },
            { key: 'FUT_Change_in_OI', label: 'FUT OI Change', format: 'number', cardClass: 'oi-card', icon: 'bi-arrow-left-right' },
            { key: 'Max_Call_OI_Strike', label: 'Max Call OI Strike', format: 'number', cardClass: 'strike-card', icon: 'bi-arrow-up-circle' },
            { key: 'Max_Call_OI', label: 'Max Call OI', format: 'number', cardClass: 'oi-card', icon: 'bi-arrow-up' },
            { key: 'Max_Put_OI_Strike', label: 'Max Put OI Strike', format: 'number', cardClass: 'strike-card', icon: 'bi-arrow-down-circle' },
            { key: 'Max_Put_OI', label: 'Max Put OI', format: 'number', cardClass: 'oi-card', icon: 'bi-arrow-down' }
        ];

        for (const metric of metrics) {
            if (summary[metric.key] !== undefined) {
                const col = document.createElement('div');
                col.className = 'col-md-4 mb-3';

                const card = document.createElement('div');
                card.className = `card summary-card ${metric.cardClass || ''}`;

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body p-3';

                // Create a row for the icon and value
                const row = document.createElement('div');
                row.className = 'd-flex align-items-center mb-2';

                // Add icon if specified
                if (metric.icon) {
                    const icon = document.createElement('i');
                    icon.className = `bi ${metric.icon} me-2`;
                    icon.style.fontSize = '1.2rem';
                    row.appendChild(icon);
                }

                const value = document.createElement('div');
                value.className = 'summary-value';

                if (metric.format === 'number') {
                    const num = parseFloat(summary[metric.key]);

                    if (isNaN(num)) {
                        value.textContent = '-';
                    } else {
                        // Format based on the type of metric
                        if (metric.key === 'PCR') {
                            value.textContent = num.toFixed(2);
                            // Add color based on PCR value
                            if (num > 1.2) value.style.color = '#28a745'; // Bullish
                            else if (num < 0.8) value.style.color = '#dc3545'; // Bearish
                        } else if (metric.key.includes('Change')) {
                            value.textContent = num.toLocaleString();
                            // Add color based on change value
                            if (num > 0) value.style.color = '#28a745';
                            else if (num < 0) value.style.color = '#dc3545';
                        } else {
                            value.textContent = num.toLocaleString();
                        }
                    }
                } else {
                    value.textContent = summary[metric.key];
                }

                row.appendChild(value);
                cardBody.appendChild(row);

                const label = document.createElement('div');
                label.className = 'summary-label';
                label.textContent = metric.label;

                cardBody.appendChild(label);
                card.appendChild(cardBody);
                col.appendChild(card);
                container.appendChild(col);
            }
        }
    }

    // Function to load dates for historical data
    function loadDates() {
        if (!currentSymbol) return;

        fetch(`/api/dates?symbol=${currentSymbol}`)
            .then(response => response.json())
            .then(data => {
                dateSelector.innerHTML = '<option value="">Select a date</option>';
                data.forEach(date => {
                    const option = document.createElement('option');
                    option.value = date;
                    option.textContent = date;
                    dateSelector.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading dates:', error));
    }

    // We no longer need to load times from the API since we're using a time input field

    // Function to load historical data for a specific date and time
    function loadHistoricalData(date, time) {
        if (!currentSymbol || !date || !time) return;

        // Format time for API request (HH:MM:SS)
        let formattedTime = time;
        if (time.length === 5) {
            // If time is in HH:MM format, add seconds
            formattedTime = time + ':00';
        }

        // Show loading indicator
        historicalOptionChainBody.innerHTML = '<tr><td colspan="15" class="text-center">Loading data...</td></tr>';
        historicalSummaryCards.innerHTML = '<div class="col-12 text-center">Loading...</div>';

        // Make API request
        fetch(`/api/historical?symbol=${currentSymbol}&date=${date}&time=${formattedTime}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    historicalOptionChainBody.innerHTML = `<tr><td colspan="15" class="text-center text-danger">${data.error}</td></tr>`;
                    historicalSummaryCards.innerHTML = '';
                    return;
                }

                // Cache the data
                if (!historicalData[date]) {
                    historicalData[date] = {};
                }

                // Create a key for the time
                const timeKey = formattedTime;

                // If the API doesn't return data for this exact time, it might return data for the closest time
                // In that case, we'll use whatever time key is available in the data
                if (data.option_chain && Object.keys(data.option_chain).length > 0) {
                    // Use the first available time key
                    const availableTimeKey = Object.keys(data.option_chain)[0];

                    // Create a new object with the correct structure
                    const formattedData = {
                        summary: data.summary,
                        option_chain: {}
                    };

                    // Use the time key from the response
                    formattedData.option_chain[timeKey] = data.option_chain[availableTimeKey];

                    // Cache and display the data
                    historicalData[date][timeKey] = formattedData;
                    updateHistoricalData(formattedData);
                } else {
                    // No data available for this time
                    historicalOptionChainBody.innerHTML = '<tr><td colspan="15" class="text-center">No data available for this time</td></tr>';
                    historicalSummaryCards.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('Error loading historical data:', error);
                historicalOptionChainBody.innerHTML = `<tr><td colspan="15" class="text-center text-danger">Error loading data: ${error.message}</td></tr>`;
                historicalSummaryCards.innerHTML = '';
            });
    }

    // Function to update historical data in the UI
    function updateHistoricalData(data) {
        // Display summary cards
        if (data.summary && data.summary.length > 0) {
            displaySummaryCards(data.summary[0], historicalSummaryCards);
        }

        // Get the selected time
        const selectedTime = timeSelector.value;

        // If no time is selected or the time doesn't exist in the data, return
        if (!selectedTime || !data.option_chain[selectedTime]) {
            historicalOptionChainBody.innerHTML = '<tr><td colspan="15" class="text-center">Select a time to view historical data</td></tr>';
            return;
        }

        // Display option chain for the selected time
        historicalOptionChainBody.innerHTML = '';

        // Sort by strike price
        data.option_chain[selectedTime].sort((a, b) => parseFloat(a.strike) - parseFloat(b.strike));

        // Find ATM strike (closest to spot price)
        let spotPrice = 0;
        if (data.summary && data.summary.length > 0 && data.summary[0].Spot_LTP) {
            spotPrice = parseFloat(data.summary[0].Spot_LTP);
        }

        let atmStrike = data.option_chain[selectedTime].length > 0 ? data.option_chain[selectedTime][0].strike : 0;
        let minDiff = data.option_chain[selectedTime].length > 0 ? Math.abs(parseFloat(data.option_chain[selectedTime][0].strike) - spotPrice) : Infinity;

        for (const option of data.option_chain[selectedTime]) {
            const strike = parseFloat(option.strike);
            const diff = Math.abs(strike - spotPrice);
            if (diff < minDiff) {
                minDiff = diff;
                atmStrike = option.strike;
            }

            const row = document.createElement('tr');

            // Check if this is the ATM strike
            if (option.strike === atmStrike) {
                row.classList.add('atm-strike');
            }

            // CE data
            const ceData = option.data;

            // CE OI
            const ceOI = ceData.CE_OI || 0;
            const ceOICell = document.createElement('td');
            ceOICell.textContent = ceOI.toLocaleString();
            row.appendChild(ceOICell);

            // CE OI Change
            const ceOIChange = ceData.CE_OI_Change || 0;
            const ceOIChangeCell = document.createElement('td');
            ceOIChangeCell.textContent = ceOIChange.toLocaleString();
            if (ceOIChange > 0) ceOIChangeCell.classList.add('call-positive');
            if (ceOIChange < 0) ceOIChangeCell.classList.add('call-negative');
            row.appendChild(ceOIChangeCell);

            // CE Open
            const ceOpen = ceData.CE_Open || 0;
            const ceOpenCell = document.createElement('td');
            ceOpenCell.textContent = ceOpen.toLocaleString();
            row.appendChild(ceOpenCell);

            // CE High
            const ceHigh = ceData.CE_High || 0;
            const ceHighCell = document.createElement('td');
            ceHighCell.textContent = ceHigh.toLocaleString();
            row.appendChild(ceHighCell);

            // CE Low
            const ceLow = ceData.CE_Low || 0;
            const ceLowCell = document.createElement('td');
            ceLowCell.textContent = ceLow.toLocaleString();
            row.appendChild(ceLowCell);

            // CE LTP
            const ceLTP = ceData.CE_LTP || 0;
            const ceLTPCell = document.createElement('td');
            ceLTPCell.textContent = ceLTP.toLocaleString();
            row.appendChild(ceLTPCell);

            // CE LTP Change
            const ceLTPChange = ceData.CE_LTP_Change || 0;
            const ceLTPChangeCell = document.createElement('td');
            ceLTPChangeCell.textContent = ceLTPChange.toLocaleString();
            if (ceLTPChange > 0) ceLTPChangeCell.classList.add('call-positive');
            if (ceLTPChange < 0) ceLTPChangeCell.classList.add('call-negative');
            row.appendChild(ceLTPChangeCell);

            // Strike Price
            const strikeCell = document.createElement('td');
            strikeCell.textContent = option.strike;
            strikeCell.classList.add('text-center', 'bg-warning');
            row.appendChild(strikeCell);

            // PE data
            const peData = option.data;

            // PE LTP Change
            const peLTPChange = peData.PE_LTP_Change || 0;
            const peLTPChangeCell = document.createElement('td');
            peLTPChangeCell.textContent = peLTPChange.toLocaleString();
            if (peLTPChange > 0) peLTPChangeCell.classList.add('put-positive');
            if (peLTPChange < 0) peLTPChangeCell.classList.add('put-negative');
            row.appendChild(peLTPChangeCell);

            // PE LTP
            const peLTP = peData.PE_LTP || 0;
            const peLTPCell = document.createElement('td');
            peLTPCell.textContent = peLTP.toLocaleString();
            row.appendChild(peLTPCell);

            // PE Low
            const peLow = peData.PE_Low || 0;
            const peLowCell = document.createElement('td');
            peLowCell.textContent = peLow.toLocaleString();
            row.appendChild(peLowCell);

            // PE High
            const peHigh = peData.PE_High || 0;
            const peHighCell = document.createElement('td');
            peHighCell.textContent = peHigh.toLocaleString();
            row.appendChild(peHighCell);

            // PE Open
            const peOpen = peData.PE_Open || 0;
            const peOpenCell = document.createElement('td');
            peOpenCell.textContent = peOpen.toLocaleString();
            row.appendChild(peOpenCell);

            // PE OI Change
            const peOIChange = peData.PE_OI_Change || 0;
            const peOIChangeCell = document.createElement('td');
            peOIChangeCell.textContent = peOIChange.toLocaleString();
            if (peOIChange > 0) peOIChangeCell.classList.add('put-positive');
            if (peOIChange < 0) peOIChangeCell.classList.add('put-negative');
            row.appendChild(peOIChangeCell);

            // PE OI
            const peOI = peData.PE_OI || 0;
            const peOICell = document.createElement('td');
            peOICell.textContent = peOI.toLocaleString();
            row.appendChild(peOICell);

            historicalOptionChainBody.appendChild(row);
        }
    }

    // Function to start auto-refresh
    function startAutoRefresh() {
        stopAutoRefresh();
        if (autoRefresh && currentSymbol) {
            refreshInterval = setInterval(() => {
                loadLiveData();
                refreshStatus.textContent = `Last refresh: ${new Date().toLocaleTimeString()}`;
            }, 1000);
        }
    }

    // Function to stop auto-refresh
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    // Function to load OI data
    function loadOIData() {
        fetch('/api/oi-data')
            .then(response => response.json())
            .then(data => {
                // Update UI with OI data
                updateOIData(data);
            })
            .catch(error => console.error('Error loading OI data:', error));
    }

    // Function to update OI data in the UI
    function updateOIData(data) {
        // Update last updated timestamp
        document.getElementById('oiLastUpdated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

        // Display OI data in the table
        const oiDataBody = document.getElementById('oiDataBody');
        oiDataBody.innerHTML = '';

        for (const symbolData of data) {
            const row = document.createElement('tr');

            // Symbol
            const symbolCell = document.createElement('td');
            symbolCell.textContent = symbolData.symbol || '';
            row.appendChild(symbolCell);

            // FUT OI
            const futOICell = document.createElement('td');
            futOICell.textContent = (symbolData.FUT_OI || 0).toLocaleString();
            row.appendChild(futOICell);

            // FUT Change in OI
            const futOIChangeCell = document.createElement('td');
            const futOIChange = symbolData.FUT_Change_in_OI || 0;
            futOIChangeCell.textContent = futOIChange.toLocaleString();
            if (futOIChange > 0) futOIChangeCell.classList.add('call-positive');
            if (futOIChange < 0) futOIChangeCell.classList.add('call-negative');
            row.appendChild(futOIChangeCell);

            // Max Call Change in OI Strike
            const maxCallChangeOIStrikeCell = document.createElement('td');
            maxCallChangeOIStrikeCell.textContent = (symbolData.Max_Call_Change_in_OI_Strike || 0).toLocaleString();
            row.appendChild(maxCallChangeOIStrikeCell);

            // Max Call Change in OI
            const maxCallChangeOICell = document.createElement('td');
            const maxCallChangeOI = symbolData.Max_Call_Change_in_OI || 0;
            maxCallChangeOICell.textContent = maxCallChangeOI.toLocaleString();
            if (maxCallChangeOI > 0) maxCallChangeOICell.classList.add('call-positive');
            if (maxCallChangeOI < 0) maxCallChangeOICell.classList.add('call-negative');
            row.appendChild(maxCallChangeOICell);

            // Max Call OI Strike
            const maxCallOIStrikeCell = document.createElement('td');
            maxCallOIStrikeCell.textContent = (symbolData.Max_Call_OI_Strike || 0).toLocaleString();
            row.appendChild(maxCallOIStrikeCell);

            // Max Call OI
            const maxCallOICell = document.createElement('td');
            maxCallOICell.textContent = (symbolData.Max_Call_OI || 0).toLocaleString();
            row.appendChild(maxCallOICell);

            // SPOT LTP
            const spotLTPCell = document.createElement('td');
            spotLTPCell.textContent = (symbolData.Spot_LTP || 0).toLocaleString();
            row.appendChild(spotLTPCell);

            // Max Put OI
            const maxPutOICell = document.createElement('td');
            maxPutOICell.textContent = (symbolData.Max_Put_OI || 0).toLocaleString();
            row.appendChild(maxPutOICell);

            // Max Put OI Strike
            const maxPutOIStrikeCell = document.createElement('td');
            maxPutOIStrikeCell.textContent = (symbolData.Max_Put_OI_Strike || 0).toLocaleString();
            row.appendChild(maxPutOIStrikeCell);

            // Max Put Change in OI
            const maxPutChangeOICell = document.createElement('td');
            const maxPutChangeOI = symbolData.Max_Put_Change_in_OI || 0;
            maxPutChangeOICell.textContent = maxPutChangeOI.toLocaleString();
            if (maxPutChangeOI > 0) maxPutChangeOICell.classList.add('put-positive');
            if (maxPutChangeOI < 0) maxPutChangeOICell.classList.add('put-negative');
            row.appendChild(maxPutChangeOICell);

            // Max Put Change in OI Strike
            const maxPutChangeOIStrikeCell = document.createElement('td');
            maxPutChangeOIStrikeCell.textContent = (symbolData.Max_Put_Change_in_OI_Strike || 0).toLocaleString();
            row.appendChild(maxPutChangeOIStrikeCell);

            oiDataBody.appendChild(row);
        }

        // Update charts if we're on the OI Chart tab
        if (document.getElementById('oi-chart-tab').classList.contains('active')) {
            updateCharts(data);
        }
    }

    // Function to initialize charts
    function initCharts() {
        // Initialize OI Chart
        const oiChartOptions = {
            chart: {
                type: 'line',
                height: '100%',
                animations: {
                    enabled: false
                },
                toolbar: {
                    show: true
                },
                fontFamily: 'inherit',
                background: '#f8f9fa'
            },
            series: [],
            colors: ['#2E93fA', '#66DA26', '#FF4560', '#E91E63', '#9C27B0'],
            xaxis: {
                type: 'category',
                categories: [],
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'Open Interest',
                    style: {
                        fontSize: '14px',
                        fontWeight: 600
                    }
                },
                labels: {
                    formatter: function(val) {
                        return val.toLocaleString();
                    }
                }
            },
            title: {
                text: 'Open Interest Chart',
                align: 'center',
                style: {
                    fontSize: '16px',
                    fontWeight: 'bold'
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            markers: {
                size: 5,
                hover: {
                    size: 7
                }
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(val) {
                        return val.toLocaleString();
                    }
                }
            },
            legend: {
                position: 'top',
                fontSize: '14px'
            },
            grid: {
                borderColor: '#e0e0e0',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                }
            },
            theme: {
                mode: 'light'
            }
        };

        oiChart = new ApexCharts(document.getElementById('oiChart'), oiChartOptions);
        oiChart.render();

        // Initialize PCR Chart
        const pcrChartOptions = {
            chart: {
                type: 'line',
                height: '100%',
                animations: {
                    enabled: false
                },
                toolbar: {
                    show: true
                },
                fontFamily: 'inherit',
                background: '#f8f9fa'
            },
            series: [],
            colors: ['#FF4560'],
            xaxis: {
                type: 'category',
                categories: [],
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'PCR Ratio',
                    style: {
                        fontSize: '14px',
                        fontWeight: 600
                    }
                },
                labels: {
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            title: {
                text: 'Call vs Put OI Ratio',
                align: 'center',
                style: {
                    fontSize: '16px',
                    fontWeight: 'bold'
                }
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            markers: {
                size: 5,
                hover: {
                    size: 7
                }
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            annotations: {
                yaxis: [{
                    y: 1,
                    borderColor: '#999',
                    label: {
                        text: 'Neutral',
                        style: {
                            color: '#333',
                            background: '#FFF8E1'
                        }
                    }
                }]
            },
            grid: {
                borderColor: '#e0e0e0',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                }
            }
        };

        pcrChart = new ApexCharts(document.getElementById('pcrChart'), pcrChartOptions);
        pcrChart.render();

        // Initialize Strikes Chart
        const strikesChartOptions = {
            chart: {
                type: 'bar',
                height: '100%',
                animations: {
                    enabled: false
                },
                toolbar: {
                    show: true
                },
                fontFamily: 'inherit',
                background: '#f8f9fa'
            },
            series: [],
            colors: ['#2E93fA', '#FF4560'],
            xaxis: {
                type: 'category',
                categories: [],
                labels: {
                    style: {
                        fontSize: '12px',
                        fontWeight: 500
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'Strike Price',
                    style: {
                        fontSize: '14px',
                        fontWeight: 600
                    }
                },
                labels: {
                    formatter: function(val) {
                        return val.toLocaleString();
                    }
                }
            },
            title: {
                text: 'Max OI Strikes',
                align: 'center',
                style: {
                    fontSize: '16px',
                    fontWeight: 'bold'
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    dataLabels: {
                        position: 'top'
                    },
                    barHeight: '70%',
                    distributed: false,
                    borderRadius: 4
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return val.toLocaleString();
                },
                offsetX: 20,
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                shared: true,
                intersect: false,
                y: {
                    formatter: function(val) {
                        return val.toLocaleString();
                    }
                }
            },
            legend: {
                position: 'top',
                fontSize: '14px'
            },
            grid: {
                borderColor: '#e0e0e0',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                }
            }
        };

        strikesChart = new ApexCharts(document.getElementById('strikesChart'), strikesChartOptions);
        strikesChart.render();
    }

    // Function to update charts with data
    function updateCharts(data) {
        if (!oiChart || !pcrChart || !strikesChart) {
            initCharts();
        }

        // Get chart type
        const chartType = document.getElementById('chartTypeSelector').value;

        // Prepare data for charts
        const symbols = [];
        const futOIData = [];
        const futOIChangeData = [];
        const maxCallOIData = [];
        const maxPutOIData = [];
        const pcrData = [];
        const maxCallOIStrikes = [];
        const maxPutOIStrikes = [];

        for (const symbolData of data) {
            symbols.push(symbolData.symbol || 'Unknown');

            // FUT OI
            futOIData.push(symbolData.FUT_OI || 0);

            // FUT OI Change
            futOIChangeData.push(symbolData.FUT_Change_in_OI || 0);

            // Max Call OI
            maxCallOIData.push(symbolData.Max_Call_OI || 0);

            // Max Put OI
            maxPutOIData.push(symbolData.Max_Put_OI || 0);

            // PCR
            const callOI = symbolData.Max_Call_OI || 0;
            const putOI = symbolData.Max_Put_OI || 0;
            const pcr = callOI > 0 ? putOI / callOI : 0;
            pcrData.push(pcr);

            // Max OI Strikes
            maxCallOIStrikes.push(symbolData.Max_Call_OI_Strike || 0);
            maxPutOIStrikes.push(symbolData.Max_Put_OI_Strike || 0);
        }

        // Update OI Chart based on selected chart type
        let oiChartSeries = [];
        let oiChartTitle = '';

        switch (chartType) {
            case 'futOi':
                oiChartSeries = [{
                    name: 'Futures OI',
                    data: futOIData
                }];
                oiChartTitle = 'Futures Open Interest';
                break;
            case 'futOiChange':
                oiChartSeries = [{
                    name: 'Futures OI Change',
                    data: futOIChangeData
                }];
                oiChartTitle = 'Futures Open Interest Change';
                break;
            case 'maxOi':
                oiChartSeries = [
                    {
                        name: 'Max Call OI',
                        data: maxCallOIData
                    },
                    {
                        name: 'Max Put OI',
                        data: maxPutOIData
                    }
                ];
                oiChartTitle = 'Max Call vs Put Open Interest';
                break;
            case 'maxOiChange':
                oiChartSeries = [
                    {
                        name: 'Max Call OI Change',
                        data: data.map(d => d.Max_Call_Change_in_OI || 0)
                    },
                    {
                        name: 'Max Put OI Change',
                        data: data.map(d => d.Max_Put_Change_in_OI || 0)
                    }
                ];
                oiChartTitle = 'Max Call vs Put Open Interest Change';
                break;
        }

        // Update OI Chart
        oiChart.updateOptions({
            xaxis: {
                categories: symbols
            },
            title: {
                text: oiChartTitle
            }
        });
        oiChart.updateSeries(oiChartSeries);

        // Update PCR Chart
        pcrChart.updateOptions({
            xaxis: {
                categories: symbols
            }
        });
        pcrChart.updateSeries([{
            name: 'PCR',
            data: pcrData
        }]);

        // Update Strikes Chart
        strikesChart.updateOptions({
            xaxis: {
                categories: symbols
            }
        });
        strikesChart.updateSeries([
            {
                name: 'Max Call OI Strike',
                data: maxCallOIStrikes
            },
            {
                name: 'Max Put OI Strike',
                data: maxPutOIStrikes
            }
        ]);

        // Update last updated timestamp
        document.getElementById('oiChartLastUpdated').textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }

    // Functions for order log
    function loadOrderLogDates() {
        fetch('/api/order-log-dates')
            .then(response => response.json())
            .then(data => {
                orderLogDateSelector.innerHTML = '<option value="">All Dates</option>';
                data.forEach(date => {
                    const option = document.createElement('option');
                    option.value = date;
                    option.textContent = date;
                    orderLogDateSelector.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading order log dates:', error));
    }

    function loadOrderLogSymbols() {
        fetch('/api/order-log-symbols')
            .then(response => response.json())
            .then(data => {
                orderLogSymbolSelector.innerHTML = '<option value="">All Symbols</option>';
                data.forEach(symbol => {
                    const option = document.createElement('option');
                    option.value = symbol;
                    option.textContent = symbol;
                    orderLogSymbolSelector.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading order log symbols:', error));
    }

    function loadOrderLogData() {
        const limit = orderLogLimitSelector.value;
        let url = `/api/order-log?limit=${limit}`;
        if (currentOrderLogDate) {
            url += `&date=${currentOrderLogDate}`;
        }
        if (currentOrderLogSymbol) {
            url += `&symbol=${currentOrderLogSymbol}`;
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                // Update UI with order log data
                updateOrderLogData(data);
            })
            .catch(error => console.error('Error loading order log data:', error));
    }

    function updateOrderLogData(data) {
        // Update last updated timestamp
        orderLogLastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;

        // Display order log data in the table
        const orderLogBody = document.getElementById('orderLogBody');
        orderLogBody.innerHTML = '';

        if (data.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 12;
            cell.textContent = 'No order log data available';
            cell.className = 'text-center';
            row.appendChild(cell);
            orderLogBody.appendChild(row);
            return;
        }

        for (const order of data) {
            const row = document.createElement('tr');

            // Timestamp
            const timestampCell = document.createElement('td');
            timestampCell.textContent = order.formatted_timestamp || order.timestamp || '';
            row.appendChild(timestampCell);

            // Symbol
            const symbolCell = document.createElement('td');
            symbolCell.textContent = order.symbol || '';
            row.appendChild(symbolCell);

            // Order Type
            const orderTypeCell = document.createElement('td');
            orderTypeCell.textContent = order.order_type || '';
            if (order.order_type === 'BUY') orderTypeCell.classList.add('call-positive');
            if (order.order_type === 'SELL') orderTypeCell.classList.add('put-negative');
            row.appendChild(orderTypeCell);

            // Option Type
            const optionTypeCell = document.createElement('td');
            optionTypeCell.textContent = order.option_type || '';
            if (order.option_type === 'CE') optionTypeCell.classList.add('call-positive');
            if (order.option_type === 'PE') optionTypeCell.classList.add('put-negative');
            row.appendChild(optionTypeCell);

            // Strike
            const strikeCell = document.createElement('td');
            strikeCell.textContent = order.strike ? order.strike.toLocaleString() : '';
            row.appendChild(strikeCell);

            // Spot Price
            const spotPriceCell = document.createElement('td');
            spotPriceCell.textContent = order.spot_price ? order.spot_price.toLocaleString() : '';
            row.appendChild(spotPriceCell);

            // Option Price
            const optionPriceCell = document.createElement('td');
            optionPriceCell.textContent = order.option_price ? order.option_price.toLocaleString() : '';
            row.appendChild(optionPriceCell);

            // P&L
            const pnlCell = document.createElement('td');
            if (order.pnl !== null && order.pnl !== undefined) {
                pnlCell.textContent = order.pnl.toLocaleString();
                if (order.pnl > 0) pnlCell.classList.add('call-positive');
                if (order.pnl < 0) pnlCell.classList.add('put-negative');
            } else {
                pnlCell.textContent = '-';
            }
            row.appendChild(pnlCell);

            // Support
            const supportCell = document.createElement('td');
            supportCell.textContent = order.support ? order.support.toLocaleString() : '';
            row.appendChild(supportCell);

            // Resistance
            const resistanceCell = document.createElement('td');
            resistanceCell.textContent = order.resistance ? order.resistance.toLocaleString() : '';
            row.appendChild(resistanceCell);

            // Status
            const statusCell = document.createElement('td');
            statusCell.textContent = order.status || '';
            if (order.status === 'EXECUTED') statusCell.classList.add('call-positive');
            if (order.status === 'FAILED') statusCell.classList.add('put-negative');
            row.appendChild(statusCell);

            // Reason
            const reasonCell = document.createElement('td');
            reasonCell.textContent = order.reason || '';
            row.appendChild(reasonCell);

            orderLogBody.appendChild(row);
        }
    }

    function startOrderLogAutoRefresh() {
        stopOrderLogAutoRefresh();
        orderLogRefreshInterval = setInterval(() => {
            loadOrderLogData();
        }, 1000); // Refresh every 1 second
    }

    function stopOrderLogAutoRefresh() {
        if (orderLogRefreshInterval) {
            clearInterval(orderLogRefreshInterval);
            orderLogRefreshInterval = null;
        }
    }

    // Function to start auto-refresh for OI data
    function startOIDataAutoRefresh() {
        stopOIDataAutoRefresh();
        oiDataRefreshInterval = setInterval(() => {
            loadOIData();
        }, 1000); // Refresh every 1 second
    }

    // Function to stop auto-refresh for OI data
    function stopOIDataAutoRefresh() {
        if (oiDataRefreshInterval) {
            clearInterval(oiDataRefreshInterval);
            oiDataRefreshInterval = null;
        }
    }
</script>
{% endblock %}
