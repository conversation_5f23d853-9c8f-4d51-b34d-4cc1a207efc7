import requests
import json
import datetime
import time
import logging
import threading
from flask import jsonify
import pymysql
from models import get_db_connection

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('advance_fii')

# Cache for storing the FII data
fii_data_cache = {
    'data': None,
    'last_updated': None
}

def fetch_advance_fii_data():
    """
    Fetch advanced FII data from the Sensibull API
    """
    url = "https://oxide.sensibull.com/v1/compute/cache/fii_dii_daily"
    
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-GB,en;q=0.9',
        'origin': 'https://web.sensibull.com',
        'priority': 'u=1, i',
        'referer': 'https://web.sensibull.com/',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
        'Cookie': '_cfuvid=SRy88MMt4RCLvHHe1PY8d2QxMzKEk7SNisI4Y9pF.iw-1746266801947-0.0.1.1-604800000'
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            return data
        else:
            logger.error(f"Failed to fetch data. Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching advanced FII data: {e}")
        return None

def get_advance_fii_data(date=None):
    """
    Get advanced FII data, either from cache or by fetching fresh data
    
    Args:
        date (str, optional): Date in YYYY-MM-DD format. If None, returns all available data.
    
    Returns:
        dict: Advanced FII data
    """
    global fii_data_cache
    
    current_time = datetime.datetime.now()
    
    # Check if we need to refresh the cache (every 15 minutes)
    if (fii_data_cache['data'] is None or 
        fii_data_cache['last_updated'] is None or 
        (current_time - fii_data_cache['last_updated']).total_seconds() > 900):  # 15 minutes = 900 seconds
        
        logger.info("Fetching fresh advanced FII data")
        data = fetch_advance_fii_data()
        
        if data:
            fii_data_cache['data'] = data
            fii_data_cache['last_updated'] = current_time
            logger.info("Advanced FII data cache updated")
        else:
            logger.warning("Failed to fetch fresh data, using cached data if available")
    
    # Return data based on date filter
    if fii_data_cache['data']:
        if date:
            # Return data for specific date if available
            if date in fii_data_cache['data']['data']:
                return {
                    'data': {date: fii_data_cache['data']['data'][date]},
                    'last_updated': fii_data_cache['last_updated'].strftime('%Y-%m-%d %H:%M:%S'),
                    'year_month': fii_data_cache['data']['year_month'],
                    'key_list': fii_data_cache['data']['key_list']
                }
            else:
                return {
                    'error': f"No data available for date {date}",
                    'last_updated': fii_data_cache['last_updated'].strftime('%Y-%m-%d %H:%M:%S') if fii_data_cache['last_updated'] else None,
                    'available_dates': list(fii_data_cache['data']['data'].keys())
                }
        else:
            # Return all data
            return {
                'data': fii_data_cache['data']['data'],
                'last_updated': fii_data_cache['last_updated'].strftime('%Y-%m-%d %H:%M:%S'),
                'year_month': fii_data_cache['data']['year_month'],
                'key_list': fii_data_cache['data']['key_list']
            }
    else:
        return {
            'error': 'No data available',
            'last_updated': None
        }

def get_available_dates():
    """
    Get list of available dates in the FII data
    
    Returns:
        list: List of available dates
    """
    global fii_data_cache
    
    if fii_data_cache['data'] and 'data' in fii_data_cache['data']:
        return list(fii_data_cache['data']['data'].keys())
    else:
        return []

def start_background_refresh():
    """
    Start a background thread to periodically refresh the FII data cache
    """
    def refresh_task():
        while True:
            try:
                logger.info("Background refresh task: Fetching fresh advanced FII data")
                data = fetch_advance_fii_data()
                
                if data:
                    global fii_data_cache
                    fii_data_cache['data'] = data
                    fii_data_cache['last_updated'] = datetime.datetime.now()
                    logger.info("Background refresh task: Advanced FII data cache updated")
            except Exception as e:
                logger.error(f"Background refresh task: Error refreshing data: {e}")
            
            # Sleep for 15 minutes
            time.sleep(900)
    
    # Start the background thread
    refresh_thread = threading.Thread(target=refresh_task, daemon=True)
    refresh_thread.start()
    logger.info("Background refresh thread started")

# Initialize the cache when the module is imported
def init_cache():
    """Initialize the cache with fresh data"""
    data = fetch_advance_fii_data()
    if data:
        global fii_data_cache
        fii_data_cache['data'] = data
        fii_data_cache['last_updated'] = datetime.datetime.now()
        logger.info("Advanced FII data cache initialized")
    else:
        logger.warning("Failed to initialize advanced FII data cache")
