-- MySQL script to migrate all tables from option_Chain_data to option_chain_db
-- This script will:
-- 1. Create each table in the target database if it doesn't exist
-- 2. Copy all data from source to target

-- Set variables
SET @source_db = 'option_Chain_data';
SET @target_db = 'option_chain_db';

-- Create procedure to migrate a single table
DELIMITER //
CREATE PROCEDURE MigrateTable(IN source_schema VARCHAR(64), IN target_schema VARCHAR(64), IN table_name VARCHAR(64))
BEGIN
    DECLARE create_stmt TEXT;
    DECLARE column_list TEXT;
    
    -- Get CREATE TABLE statement
    SELECT REPLACE(CONCAT('CREATE TABLE IF NOT EXISTS ', target_schema, '.', table_name, ' ', 
                  SUBSTR(CREATE_TABLE, LOCATE('(', CREATE_TABLE))),
                  source_schema, target_schema) INTO create_stmt
    FROM (
        SELECT CONCAT('CREATE TABLE ', table_name, ' ', 
               SUBSTR(CREATE_TABLE, LOCATE('(', CREATE_TABLE))) AS CREATE_TABLE
        FROM (
            SELECT table_name, CREATE_TABLE
            FROM information_schema.TABLES
            WHERE table_schema = source_schema AND table_name = table_name
        ) AS t
    ) AS s;
    
    -- Execute CREATE TABLE statement
    SET @create_sql = create_stmt;
    PREPARE stmt FROM @create_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Get column list
    SELECT GROUP_CONCAT(COLUMN_NAME) INTO column_list
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = source_schema AND TABLE_NAME = table_name
    ORDER BY ORDINAL_POSITION;
    
    -- Copy data
    SET @copy_sql = CONCAT('INSERT INTO ', target_schema, '.', table_name, 
                          ' SELECT * FROM ', source_schema, '.', table_name);
    PREPARE stmt FROM @copy_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Report completion
    SELECT CONCAT('Migrated table ', table_name, ' from ', source_schema, ' to ', target_schema) AS message;
END //
DELIMITER ;

-- Create procedure to migrate all tables
DELIMITER //
CREATE PROCEDURE MigrateAllTables(IN source_schema VARCHAR(64), IN target_schema VARCHAR(64))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = source_schema;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Create target database if it doesn't exist
    SET @create_db = CONCAT('CREATE DATABASE IF NOT EXISTS ', target_schema);
    PREPARE stmt FROM @create_db;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- Process each table
    OPEN cur;
    read_loop: LOOP
        FETCH cur INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Check if table exists in target
        SET @table_exists = 0;
        SELECT COUNT(*) INTO @table_exists
        FROM information_schema.TABLES
        WHERE TABLE_SCHEMA = target_schema AND TABLE_NAME = table_name;
        
        IF @table_exists > 0 THEN
            -- Drop existing table
            SET @drop_sql = CONCAT('DROP TABLE ', target_schema, '.', table_name);
            PREPARE stmt FROM @drop_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            SELECT CONCAT('Dropped existing table ', table_name, ' from ', target_schema) AS message;
        END IF;
        
        -- Migrate table
        CALL MigrateTable(source_schema, target_schema, table_name);
    END LOOP;
    CLOSE cur;
    
    -- Clean up
    DROP PROCEDURE IF EXISTS MigrateTable;
    
    SELECT CONCAT('Migration from ', source_schema, ' to ', target_schema, ' completed.') AS message;
END //
DELIMITER ;

-- Execute migration
CALL MigrateAllTables(@source_db, @target_db);

-- Clean up
DROP PROCEDURE IF EXISTS MigrateAllTables;
