{% extends "base.html" %}

{% block title %}User Profile{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
<style>
    .profile-header {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .profile-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 40px;
        margin-right: 20px;
    }
    .profile-info {
        flex-grow: 1;
    }
    .profile-section {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .profile-section h3 {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    .subscription-card {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
        height: 100%;
    }
    .subscription-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .subscription-card.active {
        border: 2px solid #28a745;
    }
    .subscription-card .card-header {
        font-weight: bold;
        text-align: center;
        padding: 15px;
    }
    .subscription-card .card-body {
        padding: 20px;
    }
    .subscription-price {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 10px 0;
    }
    .subscription-duration {
        text-align: center;
        color: #6c757d;
        margin-bottom: 20px;
    }
    .feature-list {
        list-style-type: none;
        padding: 0;
    }
    .feature-list li {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .feature-list li:last-child {
        border-bottom: none;
    }
    .feature-list li i {
        margin-right: 10px;
        color: #28a745;
    }
    .badge-subscription {
        background-color: #28a745;
        color: white;
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 20px;
    }
    .badge-expiry {
        background-color: #dc3545;
        color: white;
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 20px;
    }
    .badge-expiry.active {
        background-color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">User Profile</h4>
            </div>
            <div class="card-body">
                <p class="lead">Manage your account information and subscription details.</p>
            </div>
        </div>
    </div>
</div>

<div class="profile-header d-flex align-items-center">
    <div class="profile-avatar">
        {{ current_user.username[0].upper() }}
    </div>
    <div class="profile-info">
        <h2>{{ current_user.username }}</h2>
        <p class="text-muted">{{ current_user.email }}</p>
        <div>
            <span class="badge badge-subscription">{{ current_user.subscription_type.upper() }}</span>
            {% if current_user.subscription_expiry > now() %}
                <span class="badge badge-expiry active">Active until {{ current_user.subscription_expiry.strftime('%Y-%m-%d') }}</span>
            {% else %}
                <span class="badge badge-expiry">Expired on {{ current_user.subscription_expiry.strftime('%Y-%m-%d') }}</span>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="profile-section">
            <h3><i class="bi bi-person-circle"></i> Account Information</h3>
            <div class="mb-3">
                <label class="form-label">Username</label>
                <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">Email</label>
                <input type="email" class="form-control" value="{{ current_user.email }}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">Account Type</label>
                <input type="text" class="form-control" value="{{ current_user.role.capitalize() }}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">Member Since</label>
                <input type="text" class="form-control" value="{{ current_user.created_at.strftime('%Y-%m-%d') }}" readonly>
            </div>
            <button class="btn btn-primary" disabled>Edit Profile (Coming Soon)</button>
        </div>
    </div>

    <div class="col-md-6">
        <div class="profile-section">
            <h3><i class="bi bi-credit-card"></i> Current Subscription</h3>
            {% if subscription %}
                <div class="card subscription-card active">
                    <div class="card-header bg-{{ subscription.name }}">
                        {{ subscription.name.upper() }} PLAN
                    </div>
                    <div class="card-body">
                        <div class="subscription-price">${{ subscription.price }}</div>
                        <div class="subscription-duration">{{ subscription.duration_days }} days</div>
                        <ul class="feature-list">
                            {% if subscription.features %}
                                {% set features_dict = subscription.features %}
                                {% if features_dict is string %}
                                    {% set features_dict = subscription.features|tojson|safe %}
                                {% endif %}
                                {% for feature, value in features_dict.items() %}
                                    <li><i class="bi bi-check-circle-fill"></i> {{ feature.replace('_', ' ').title() }}: {{ value }}</li>
                                {% endfor %}
                            {% else %}
                                <li><i class="bi bi-check-circle-fill"></i> Basic features</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>

                <div class="mt-3">
                    <p>
                        {% if current_user.subscription_expiry > now() %}
                            Your subscription is active until <strong>{{ current_user.subscription_expiry.strftime('%Y-%m-%d') }}</strong>.
                        {% else %}
                            Your subscription has expired. Please renew to continue enjoying premium features.
                        {% endif %}
                    </p>
                    <a href="{{ url_for('subscriptions') }}" class="btn btn-success">Manage Subscription</a>
                </div>
            {% else %}
                <p>No subscription information available.</p>
                <a href="{{ url_for('subscriptions') }}" class="btn btn-primary">View Subscription Plans</a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="profile-section">
            <h3><i class="bi bi-gear-fill"></i> Account Settings</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Change Password</label>
                        <div class="input-group">
                            <input type="password" class="form-control" placeholder="New Password" disabled>
                            <button class="btn btn-outline-secondary" type="button" disabled>Update</button>
                        </div>
                        <small class="text-muted">Password change functionality coming soon.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Notification Preferences</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" disabled>
                            <label class="form-check-label" for="emailNotifications">Email Notifications</label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="smsNotifications" disabled>
                            <label class="form-check-label" for="smsNotifications">SMS Notifications</label>
                        </div>
                        <small class="text-muted">Notification settings coming soon.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Any JavaScript for the profile page can go here
    });
</script>
{% endblock %}
