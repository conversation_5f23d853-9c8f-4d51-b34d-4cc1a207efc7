import pymysql
import configparser
import os

def get_mysql_config():
    """Get MySQL connection details from config file or create if not exists"""
    config = configparser.ConfigParser()

    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': '',
            'database': 'option_chain_db'
        }

def create_mysql_tables():
    """Create the necessary tables in MySQL"""
    mysql_config = get_mysql_config()

    try:
        # Connect to MySQL
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Create a cursor
        cursor = conn.cursor()

        # Drop existing tables if they exist
        print("Dropping existing tables...")
        cursor.execute("DROP TABLE IF EXISTS option_data_need")
        cursor.execute("DROP TABLE IF EXISTS option_data")
        conn.commit()

        # Create option_data_need table
        print("Creating option_data_need table...")
        cursor.execute("""
        CREATE TABLE option_data_need (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp VARCHAR(50),
            symbol VARCHAR(50),
            Spot_LTP FLOAT,
            FUT_LTP FLOAT,
            VIX_LTP FLOAT,
            High FLOAT,
            Low FLOAT,
            Spot_LTP_Change FLOAT,
            FUT_LTP_Change FLOAT,
            VIX_LTP_Change FLOAT,
            FUT_OI INT,
            FUT_Change_in_OI INT,
            Total_Call_OI INT,
            Total_Put_OI INT,
            Total_Call_Change_in_OI INT,
            Total_Put_Change_in_OI INT,
            Max_Call_OI INT,
            Max_Put_OI INT,
            Max_Call_OI_Strike FLOAT,
            Max_Put_OI_Strike FLOAT,
            Max_Call_Change_in_OI INT,
            Max_Put_Change_in_OI INT,
            Max_Call_Change_in_OI_Strike FLOAT,
            Max_Put_Change_in_OI_Strike FLOAT,
            PCR FLOAT,
            Max_Pain_Strike FLOAT
        )
        """)
        conn.commit()
        print("option_data_need table created successfully")

        # Create option_data table
        print("Creating option_data table...")
        cursor.execute("""
        CREATE TABLE option_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp VARCHAR(50),
            symbol VARCHAR(50),
            Strike FLOAT,
            CE_Instrument_Type VARCHAR(50),
            CE_LTP FLOAT,
            CE_LTP_Change FLOAT,
            CE_OI INT,
            CE_OI_Change INT,
            CE_IV_Spot FLOAT,
            CE_Delta_Spot FLOAT,
            CE_Gamma_Spot FLOAT,
            CE_Theta_Spot FLOAT,
            CE_Vega_Spot FLOAT,
            PE_Instrument_Type VARCHAR(50),
            PE_LTP FLOAT,
            PE_LTP_Change FLOAT,
            PE_OI INT,
            PE_OI_Change INT,
            PE_IV_Spot FLOAT,
            PE_Delta_Spot FLOAT,
            PE_Gamma_Spot FLOAT,
            PE_Theta_Spot FLOAT,
            PE_Vega_Spot FLOAT
        )
        """)
        conn.commit()
        print("option_data table created successfully")

        # Close connection
        conn.close()

        print("\nMySQL tables created successfully!")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    create_mysql_tables()
